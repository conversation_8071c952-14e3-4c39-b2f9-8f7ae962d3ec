<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookFrontMatterMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookFrontMatterMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookFrontMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookFrontMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\FrontMatter $fromNode, CodeBook\FrontMatter $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $actual->getTitlePage() && $actual->getTitlePage()->setUlid('uuid');
        $actual->getTitlePage() && $actual->getTitlePage()->setNodeId('nodeId');
        $actual->getCopyrightPage() && $actual->getCopyrightPage()->setUlid('uuid');
        $actual->getCopyrightPage() && $actual->getCopyrightPage()->setNodeId('nodeId');
        $actual->getPublisherNote() && $actual->getPublisherNote()->setUlid('uuid');
        $actual->getPublisherNote() && $actual->getPublisherNote()->setNodeId('nodeId');
        $actual->getPreface() && $actual->getPreface()->setUlid('uuid');
        $actual->getPreface() && $actual->getPreface()->setNodeId('nodeId');
        $actual->getForeword() && $actual->getForeword()->setUlid('uuid');
        $actual->getForeword() && $actual->getForeword()->setNodeId('nodeId');

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\FrontMatter();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\FrontMatter();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromTitlePage = new BaseBook\TitlePage();
        $fromNode->addChild($fromTitlePage);
        $fromNode->setTitlePage($fromTitlePage);
        $toTitlePage = new CodeBook\TitlePage();
        $expected->addChild($toTitlePage);
        $expected->setTitlePage($toTitlePage);
        yield 'titlePage' => [deep_copy($fromNode), deep_copy($expected)];

        $fromCopyright = new BaseBook\CopyrightPage();
        $fromNode->addChild($fromCopyright);
        $fromNode->setCopyrightPage($fromCopyright);
        $toCopyright = new CodeBook\CopyrightPage();
        $expected->addChild($toCopyright);
        $expected->setCopyrightPage($toCopyright);
        yield 'copyrightPage' => [deep_copy($fromNode), deep_copy($expected)];

        $fromPublisherNote = new BaseBook\PublisherNote();
        $fromNode->addChild($fromPublisherNote);
        $fromNode->setPublisherNote($fromPublisherNote);
        $toPublisherNote = new CodeBook\PublisherNote();
        $expected->addChild($toPublisherNote);
        $expected->setPublisherNote($toPublisherNote);
        yield 'publisherNote' => [deep_copy($fromNode), deep_copy($expected)];

        $fromPreface = new BaseBook\Preface();
        $fromNode->addChild($fromPreface);
        $fromNode->setPreface($fromPreface);
        $toPreface = new CodeBook\Preface();
        $expected->addChild($toPreface);
        $expected->setPreface($toPreface);
        yield 'preface' => [deep_copy($fromNode), deep_copy($expected)];

        $fromForeword = new BaseBook\Foreword();
        $fromNode->addChild($fromForeword);
        $fromNode->setForeword($fromForeword);
        $toForeword = new CodeBook\Foreword();
        $expected->addChild($toForeword);
        $expected->setForeword($toForeword);
        yield 'foreword' => [deep_copy($fromNode), deep_copy($expected)];

        $fromChapter = new BaseBook\Chapter();
        $fromNode->addChild($fromChapter);
        $toChapter = new CodeBook\Chapter();
        $expected->addChild($toChapter);
        yield 'chapter1' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setChildren([$fromTitlePage, $fromCopyright, $fromPublisherNote, $fromPreface, $fromForeword, $fromChapter]);
        $fromNode->addChild($fromChapter);
        $toChapter = new CodeBook\Chapter();
        $expected->addChild($toChapter);
        yield 'chapter2' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
