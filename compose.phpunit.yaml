# Development environment override
services:
  phpunit:
    container_name: ct-phpunit-${PHP_VERSION:-8.2}
    image: ${IMAGE:-php:latest}
    working_dir: /app
    volumes:
      - ./:/app
      - ./frankenphp/Caddyfile:/etc/frankenphp/Caddyfile:ro
      - ./frankenphp/conf.d/20-app.dev.ini:/usr/local/etc/php/app.conf.d/20-app.dev.ini:ro
      - ./frankenphp/supervisord.conf:/etc/supervisor/supervisord.conf:ro
      - ./frankenphp/supervisord-app.conf:/etc/supervisor/conf.d/supervisord-app.conf:ro
      - ${COMPOSER_CACHE_DIR:-/tmp/composer/}:/composer-cache/
      - var_cache:/app/var/
    environment:
      APP_ENV: "${APP_ENV:-test}"
      COMPOSER_CACHE_DIR: '/composer-cache/'
      DATABASE_URL: 'mysql://test_user:test_pass@mariadb:3306/test_db?serverVersion=mariadb-12.0.2'
    tty: true
    depends_on:
      - mariadb

  mariadb:
    image: mariadb:12.0.2
    container_name: mariadb
    restart: unless-stopped
    environment:
      MARIADB_ROOT_PASSWORD: rootpassword
      MARIADB_DATABASE: test_db
      MARIADB_USER: test_user
      MARIADB_PASSWORD: test_pass
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql

volumes:
  mariadb_data:
  var_cache:
