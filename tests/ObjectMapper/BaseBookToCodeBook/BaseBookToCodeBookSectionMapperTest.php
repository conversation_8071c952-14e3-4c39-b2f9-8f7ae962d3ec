<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookSectionMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookSectionMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookSectionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookSectionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Section $fromNode, CodeBook\Section $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Section();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Section();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        $expected->setRole('');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield 'indexNumber' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setReserveCount(100);
        $expected->setReserveCount(100);
        yield 'reserveCount' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDisplayLevel(100);
        $expected->setDisplayLevel(100);
        yield 'displayLevel' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setHistory('history');
        $expected->setHistory('history');
        yield 'history' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setObjectivesTitle('objectivesTitle');
        $expected->setObjectivesTitle('objectivesTitle');
        yield 'objectivesTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setObjectives('objectives');
        $expected->setObjectives('objectives');
        yield 'objectives' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setAbstractTitle('abstractTitle');
        $expected->setAbstractTitle('abstractTitle');
        yield 'abstractTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setAbstract('abstract');
        $expected->setAbstract('abstract');
        yield 'abstract' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setKeywordsTitle('keywordsTitle');
        $expected->setKeywordsTitle('keywordsTitle');
        yield 'keywordsTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setKeywords('keywords');
        $expected->setKeywords('keywords');
        yield 'keywords' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setChildren([new BaseBook\Section(), new BaseBook\Section()]);
        $expected->setChildren([new CodeBook\Section(), new CodeBook\Section()]);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
