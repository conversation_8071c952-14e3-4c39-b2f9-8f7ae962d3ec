name: Shared Deploy Workflow

on:
  workflow_call:
    inputs:
      CR_TAG:
        description: Tag used for container image.
        required: true
        type: string
      APP_RUNTIME_ENV:
        description: The environment to pull Symfony secrets from.
        required: true
        type: string
      PHP_VERSION:
        description: PHP version of image
        required: false
        type: string
        default: 8.2
    secrets:
      CCT_COMPOSER_AUTH_JSON:
        description: auth.json contents for CCT projects.
        required: true
      SYMFONY_DECRYPTION_SECRET:
        description: The decryption key used for decoding Symfony secrets.
        required: true

env:
  CONTAINER_IMAGE: ${{ vars.ICC_CR_DOMAIN }}/${{ vars.CR_REPOSITORY }}
  CONTAINER_NAME: ct-api

concurrency:
  group: ${{ github.workflow }}-shared-build-${{ inputs.CR_TAG }}
  cancel-in-progress: false

jobs:
  shared-build:
    name: Build
    runs-on: self-hosted

    steps:
      - name: Reset workspace permissions
        run: |
          sudo chmod -R u+rwX $GITHUB_WORKSPACE
          sudo chown -R $(id -u):$(id -g) $GITHUB_WORKSPACE

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set Git identity
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Set Docker tags
        id: docker_tags
        run: |
          TAGS="${{ env.CONTAINER_IMAGE }}:${{ inputs.CR_TAG }}"
          if [[ "${GITHUB_REF}" == refs/tags/* ]]; then
            TAGS="$TAGS,${{ env.CONTAINER_IMAGE }}:${GITHUB_REF##refs/tags/}"
          fi
          echo "tags=$TAGS" >> $GITHUB_OUTPUT

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-2
          role-to-assume: arn:aws:iam::286574893818:role/gh-actions-ecr-role

      - name: Login to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push to ECR
        uses: docker/build-push-action@v5
        env:
          DOCKER_BUILDKIT: 1
          SERVER_NAME: :80
        with:
          context: .
          push: true
          tags: ${{ vars.ICC_CR_DOMAIN }}/${{ vars.CR_REPOSITORY }}:${{ inputs.CR_TAG }}
          build-args: |
            APP_RUNTIME_ENV=${{ inputs.APP_RUNTIME_ENV }}
            PHP_VERSION=${{ inputs.PHP_VERSION }}
          secrets: |
            composer-auth=${{ secrets.CCT_COMPOSER_AUTH_JSON }}
            symfony-decryption-secret=${{ secrets.SYMFONY_DECRYPTION_SECRET }}
