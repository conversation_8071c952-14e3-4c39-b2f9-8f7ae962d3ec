<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideForewordMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Foreword $element, CodeBook\Foreword $entity): CodeBook\Foreword
    {
        $this->mapTitleGroup($element, $entity);

        $entity->setTocEntry($element->hasTocEntry());
        $entity->setBody($element->getBody());

        return $entity;
    }
}
