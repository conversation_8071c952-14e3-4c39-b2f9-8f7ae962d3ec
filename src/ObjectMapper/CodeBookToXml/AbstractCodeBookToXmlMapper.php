<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CommonBook\Attribute\CommonAttributes;
use App\Entity\CommonBook\Attribute\RevisionAttributes;
use App\Entity\CommonBook\Field\TitleGroup as CodeBookTitleGroup;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\Attribute\CommonAttributes as ElementCommonAttributes;
use App\Serializer\Encoder\Xml2\Element\Attribute\RevisionAttributes as ElementRevisionAttributes;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\Field\TitleGroupTrait;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\ShortCode;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\Body;
use Symfony\Contracts\Service\Attribute\Required;

abstract class AbstractCodeBookToXmlMapper
{
    protected ?CodeBookToXmlMapper $mapper;

    /** Injects service without causing circular dependency issues */
    #[Required]
    public function setCodeBookToXmlMapper(CodeBookToXmlMapper $mapper): void
    {
        $this->mapper = $mapper;
    }

    protected function mapCommonAttributes(AbstractCodeBookNode $from, AbstractElement $to): void
    {
        $to->setId($from->getNodeId());

        /** @var CommonAttributes $from */
        /** @var ElementCommonAttributes $to */
        $to->setRole($from->getRole());
        $to->setDisplay($from->getDisplay());
        $to->setVerbatim($from->isVerbatim());
        $to->setLanguage($from->getLanguage());
        $to->setAdditionalInfo($from->getAdditionalInfo());
    }

    protected function mapRevisionAttributes(AbstractCodeBookNode $from, AbstractElement $to): void
    {
        /** @var RevisionAttributes $from */
        /** @var ElementRevisionAttributes $to */
        $to->setRevisionBy($from->getRevisionBy());
        $to->setRevisionDateTime($from->getRevisionDateTime());
        $to->setRevision($from->getRevision());
        $to->setRevisionGroup($from->getRevisionGroup());
        $to->setDataChanged($from->getDataChanged());
        $to->setDataChangedIn($from->getDataChangedIn());
        $to->setRelocatedFromAttr($from->getRelocatedFromAttr());
    }

    protected function mapTitleGroup(AbstractCodeBookNode $from, AbstractElement $to): void
    {
        /**
         * @var CodeBookTitleGroup $from
         * @var TitleGroupTrait $to
         */

        $titleGroup = new TitleGroup();
        $use = false;

        if (!empty($from->getSuperTitle())) {
            $superTitle = new SuperTitle();
            $superTitle->setBody($from->getSuperTitle());
            $titleGroup->setSuperTitle($superTitle);
            $use = true;
        }
        if (!empty($from->getCommitteeDesignation())) {
            $cd = new CommitteeDesignation();
            $cd->setBody($from->getCommitteeDesignation());
            $titleGroup->setCommitteeDesignation($cd);
            $use = true;
        }
        if (!empty($from->getLabel())) {
            $label = new Label();
            $label->setBody($from->getLabel());
            $titleGroup->setLabel($label);
            $use = true;
        }
        if (!empty($from->getNumber())) {
            $number = new Number();
            $number->setBody($from->getNumber());
            $titleGroup->setNumber($number);
            $use = true;
        }
        if (!empty($from->getCorrelated())) {
            $correlated = new Correlated();
            $correlated->setBody($from->getCorrelated());
            $titleGroup->setCorrelated($correlated);
            $use = true;
        }
        if (!empty($from->getTitle())) {
            $title = new Title();
            $title->setBody($from->getTitle());
            $title->setTitleAbbreviation($from->getTitleAbbreviation());
            $title->setTitleYear($from->getTitleYear());
            $titleGroup->setTitle($title);
            $use = true;
        }
        if (!empty($from->getSubTitle())) {
            $subTitle = new SubTitle();
            $subTitle->setBody($from->getSubTitle());
            $titleGroup->setSubTitle($subTitle);
            $use = true;
        }
        if (!empty($from->getTitleHistory())) {
            $history = new History();
            $history->setBody($from->getTitleHistory());
            $titleGroup->setHistory($history);
            $use = true;
        }

        if ($use) {
            $to->setTitleGroup($titleGroup);
        }
    }

    protected function mapQrCode(AbstractCodeBookNode $from, AbstractElement $to): void
    {
        $qrCode = new QrCode();
        $shortCode = new ShortCode();
        $qrCode->setShortCode($shortCode);

        /** @var Appendix $to */
        if ($from->isQrActive()) {
            $to->setQrCode($qrCode);
        }

        $qrCode->setId($from->getQrId());
        $qrCode->setQrDisplay($from->isQrDisplay());
        $qrCode->setLevelReference($from->getQrLevelReference());
        $qrCode->setPurpose($from->getQrPurpose());
        $qrCode->setImg($from->getQrImage());
        $qrCode->setBookIcon($from->getQrBookIcon());

        $shortCode->setShortUrl($from->getQrShortUrl());
    }

    protected function createObjectives(string $title, string $body): ?Objectives
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $objectives = new Objectives();
        if (!empty($body)) {
            $bodyEl = new Body();
            $bodyEl->setBody($body);
            $objectives->setBody($bodyEl);
        }

        if (!empty($title)) {
            $titleEl = new Title();
            $titleEl->setBody($title);
            $titleGroup = new TitleGroup();
            $titleGroup->setTitle($titleEl);
            $objectives->setTitleGroup($titleGroup);
        }

        return $objectives;
    }

    protected function createNote(string $title, string $body): ?Note
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $note = new Note();
        $note->setBody($body);

        if (!empty($title)) {
            $titleEl = new Title();
            $titleEl->setBody($title);
            $titleGroup = new TitleGroup();
            $titleGroup->setTitle($titleEl);
            $note->setTitleGroup($titleGroup);
        }

        return $note;
    }

    protected function createAbstract(string $title, string $body): ?AbstractField
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $abstract = new AbstractField();
        $abstract->setBody($body);

        if (!empty($title)) {
            $titleEl = new Title();
            $titleEl->setBody($title);
            $titleGroup = new TitleGroup();
            $titleGroup->setTitle($titleEl);
            $abstract->setTitleGroup($titleGroup);
        }

        return $abstract;
    }

    protected function createKeywords(string $title, string $body): ?Keywords
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $keywords = new Keywords();
        $keywords->setBody($body);

        if (!empty($title)) {
            $titleEl = new Title();
            $titleEl->setBody($title);
            $titleGroup = new TitleGroup();
            $titleGroup->setTitle($titleEl);
            $keywords->setTitleGroup($titleGroup);
        }

        return $keywords;
    }

    protected function createCredit(string $title, string $body): ?Credit
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $credit = new Credit();
        $credit->setBody($body);

        if (!empty($title)) {
            $titleEl = new Title();
            $titleEl->setBody($title);
            $titleGroup = new TitleGroup();
            $titleGroup->setTitle($titleEl);
            $credit->setTitleGroup($titleGroup);
        }

        return $credit;
    }
}
