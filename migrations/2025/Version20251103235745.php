<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251103235745 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs;
        $this->addSql('ALTER TABLE code_book_definition_list ADD revision_by VARCHAR(100) NOT NULL, ADD revision_date_time DATETIME DEFAULT NULL, ADD revision VARCHAR(10) NOT NULL, ADD revision_group VARCHAR(20) NOT NULL, ADD data_changed VARCHAR(20) NOT NULL, ADD data_changed_in VARCHAR(100) NOT NULL, ADD relocated_from_attr VARCHAR(500) NOT NULL, ADD super_title LONGTEXT NOT NULL, ADD correlated LONGTEXT NOT NULL, ADD sub_title LONGTEXT NOT NULL, ADD title_history LONGTEXT NOT NULL, ADD committee_designation LONGTEXT NOT NULL, ADD label LONGTEXT NOT NULL, ADD number LONGTEXT NOT NULL, ADD original_number LONGTEXT NOT NULL, ADD title LONGTEXT NOT NULL, ADD title_abbreviation VARCHAR(20) NOT NULL, ADD title_year VARCHAR(20) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE code_book_definition_list DROP revision_by, DROP revision_date_time, DROP revision, DROP revision_group, DROP data_changed, DROP data_changed_in, DROP relocated_from_attr, DROP super_title, DROP correlated, DROP sub_title, DROP title_history, DROP committee_designation, DROP label, DROP number, DROP original_number, DROP title, DROP title_abbreviation, DROP title_year');
    }
}
