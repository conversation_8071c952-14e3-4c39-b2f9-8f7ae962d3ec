<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Acronym;
use App\Serializer\Encoder\Xml2\Element\Address;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\References;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\keyValue;

class PromulgatorMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'promulgator';
    }

    public static function dtoClass(): string
    {
        return Promulgator::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Promulgator);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('acronym' === $key && $value instanceof Acronym) {
                $element->setAcronym($value);
            } elseif ('address' === $key && $value instanceof Address) {
                $element->setAddress($value);
            } elseif ('references' === $key && $value instanceof References) {
                $element->setReferences($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Promulgator);

        if ($element->getAcronym()) {
            $writer->writeElement(self::CLARK . 'acronym', $element->getAcronym());
        }
        if ($element->getAddress()) {
            $writer->writeElement(self::CLARK . 'address', $element->getAddress());
        }
        if ($element->getReferences()) {
            $writer->writeElement(self::CLARK . 'references', $element->getReferences());
        }
    }
}
