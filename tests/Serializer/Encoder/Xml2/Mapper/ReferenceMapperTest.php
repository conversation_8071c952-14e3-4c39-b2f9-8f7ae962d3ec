<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\ReferenceMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\ReferenceMapper
 */
class ReferenceMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Reference::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Reference::class]);
        $this->assertInstanceOf(ReferenceMapper::class, $service->classMap[Reference::class][0]);

        $this->assertArrayHasKey(ReferenceMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[ReferenceMapper::elementName()]);
        $this->assertInstanceOf(ReferenceMapper::class, $service->elementMap[ReferenceMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Reference $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<ref %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Reference();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Reference $element, string $innerXml): void
    {
        $xml = sprintf('<ref xmlns="%s" xmlns:m="%s">%s</ref>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Reference();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $navPointerXml = '<nav-pointer-group><nav-pointer>id</nav-pointer></nav-pointer-group>';
        $element->setNavPointerGroup($navPointerXml);
        $innerXml .= $navPointerXml;
        yield 'nav-pointer-group' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(Reference $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $xml = <<<XML
<ref id="IMC2024V2.0_Ch15_PromACCA_RefStdANSI_ACCA_1_Manual_D_2023" xmlns="https://schema.iccsafe.org/book/schema/1.0" xmlns:m="http://www.w3.org/1998/Math/MathML">
    <titlegroup>
        <number>ANSI/ACCA 1 Manual D—<insert role="insert" data-changed="changed_level0" data-changed-in="IMC2024V2.0">2023</insert></number>
        <title>Residential Duct Systems</title>
    </titlegroup>
<nav-pointer-group>
    <nav-pointer rid="IMC2024V2.0_Ch06_Sec601.4">601.4</nav-pointer>
    <nav-pointer rid="IMC2024V2.0_Ch06_Sec603.2">603.2</nav-pointer>
</nav-pointer-group>
</ref>
XML;
        $element = new Reference();
        $element->setId('IMC2024V2.0_Ch15_PromACCA_RefStdANSI_ACCA_1_Manual_D_2023');
        $element->setNavPointerGroup(<<<XML
<nav-pointer-group>
    <nav-pointer rid="IMC2024V2.0_Ch06_Sec601.4">601.4</nav-pointer>
    <nav-pointer rid="IMC2024V2.0_Ch06_Sec603.2">603.2</nav-pointer>
</nav-pointer-group>
XML);

        $titleGroup = new TitleGroup();
        $titleGroup->setNumber('ANSI/ACCA 1 Manual D—<insert role="insert" data-changed="changed_level0" data-changed-in="IMC2024V2.0">2023</insert>');
        $titleGroup->setTitle('Residential Duct Systems');
        $element->setTitleGroup($titleGroup);

        yield 'IMC2024V2.0_Ch15_PromACCA_RefStdANSI_ACCA_1_Manual_D_2023' => [$element, $xml];
    }
}
