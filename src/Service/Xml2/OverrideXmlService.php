<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\OverrideXml\OverrideXmlRequest;
use App\Dto\Xml2\OverrideXml\UpdateOverrideXmlRequest;
use App\Dto\Xml2\OverrideXml\ValidateXmlRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\ObjectMapper\XmlOverrideMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class OverrideXmlService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Xml2Encoder            $xml2Encoder,
        private readonly XmlOverrideMapper      $mapper
    ) {
    }

    public function getOverrideXml(AbstractCodeBookNode $node, OverrideXmlRequest $requestDto): Response
    {
        if (!$requestDto->isOverrideChildren()) {
            $node->setChildren([]);
        }

        $xml = $this->xml2Encoder->encode($node);

        $headers = ['Content-Type' => 'text/plain; charset=utf-8'];

        if ($requestDto->isDownload()) {
            $headers['Content-Type'] = 'application/xml; charset=utf-8';
            $headers['Content-Disposition'] = sprintf(
                'attachment; filename="%s-override.xml"',
                $node->getNodeId()
            );
        }

        return new Response($xml, Response::HTTP_OK, $headers);
    }

    public function updateNodeOverrideXml(UpdateOverrideXmlRequest $requestDto): array
    {
        $nodeId = $requestDto->getNodeId();

        /** @var AbstractCodeBookNode|null $node */
        $node = $this->entityManager
            ->getRepository(AbstractCodeBookNode::class)
            ->findOneBy(['nodeId' => $nodeId]);

        if (!$node) {
            return ['error' => 'Node not found'];
        }

        $incoming = $this->xml2Encoder->decode($requestDto->getBody());
        $this->mapper->map($incoming);

        $this->entityManager->flush();

        return [
            'status' => 'updated',
            'id'     => $node->getNodeId(),
        ];
    }

    public function validateNodeXml(ValidateXmlRequest $requestDto): array
    {
        $xmlContent = $requestDto->getBody();
        $nodeId = $requestDto->getNodeId();

        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = false;

        libxml_use_internal_errors(true);

        $isValidXml = $dom->loadXML($xmlContent);
        $syntaxErrors = libxml_get_errors();
        libxml_clear_errors();

        $response = [
            'nodeId' => $nodeId,
            'valid'  => false,
            'stage'  => null,
            'errors' => [],
        ];

        if (!$isValidXml) {
            foreach ($syntaxErrors as $error) {
                $response['errors'][] = [
                    'stage'   => 'syntax',
                    'level'   => $this->getLibXmlErrorLevelName($error->level),
                    'code'    => $error->code,
                    'message' => trim($error->message),
                    'line'    => $error->line,
                    'column'  => $error->column,
                ];
            }

            return $response;
        }

        // If both syntax and schema passed
        $response['valid'] = true;

        return $response;
    }

    private function getLibXmlErrorLevelName(int $level): string
    {
        return match ($level) {
            LIBXML_ERR_WARNING => 'warning',
            LIBXML_ERR_ERROR   => 'error',
            LIBXML_ERR_FATAL   => 'fatal',
            default            => 'unknown',
        };
    }
}
