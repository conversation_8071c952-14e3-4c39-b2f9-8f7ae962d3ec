<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlVolumeMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlVolumeMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlVolumeMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlVolumeMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Volume $from, Xml2\Volume $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Volume();
        $from->setNodeId('');
        $from->setRole('');
        $expected = new Xml2\Volume();
        $metadata = new Xml2\Metadata();
        $expected->setMetadata($metadata);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $from->setCustomerId('customerId');
        $expected->setCustomerId('customerId');
        yield 'customerId' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleType(TitleType::CODE);
        $expected->setTitleType(TitleType::CODE);
        yield 'titleType' => [deep_copy($from), deep_copy($expected)];

        $from->setGoverningType(GoverningType::MUNICIPALITY);
        $expected->setGoverningType(GoverningType::MUNICIPALITY);
        yield 'governingType' => [deep_copy($from), deep_copy($expected)];

        $from->setMetaTitle('metaTitle');
        $metadata->addMeta('title', 'metaTitle');
        yield 'metaTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setEdition('edition');
        $metadata->addMeta('edition', 'edition');
        yield 'edition' => [deep_copy($from), deep_copy($expected)];

        $from->setParentDocument('parentDocument');
        $metadata->addMeta('parent-document', 'parentDocument');
        yield 'parentDocument' => [deep_copy($from), deep_copy($expected)];

        $from->setPublicationId('publicationId');
        $metadata->addMeta('publication-id', 'publicationId');
        yield 'publicationId' => [deep_copy($from), deep_copy($expected)];

        $from->setYear('year');
        $metadata->addMeta('year', 'year');
        yield 'year' => [deep_copy($from), deep_copy($expected)];

        $from->setPublicationAbbreviation('publicationAbbreviation');
        $metadata->addMeta('publication-abbrev', 'publicationAbbreviation');
        yield 'publicationAbbreviation' => [deep_copy($from), deep_copy($expected)];

        $from->setVersion('version');
        $metadata->addMeta('version', 'version');
        yield 'version' => [deep_copy($from), deep_copy($expected)];

        $from->setOrigin('origin');
        $metadata->addMeta('origin', 'origin');
        yield 'origin' => [deep_copy($from), deep_copy($expected)];

        $from->setDateOrigin('dateOrigin');
        $metadata->addMeta('date-origin', 'dateOrigin');
        yield 'dateOrigin' => [deep_copy($from), deep_copy($expected)];

        $from->setModifiedBy('modifiedBy');
        $metadata->addMeta('modified-by', 'modifiedBy');
        yield 'modifiedBy' => [deep_copy($from), deep_copy($expected)];

        $from->setDateUpdated('dateUpdated');
        $metadata->addMeta('date-updated', 'dateUpdated');
        yield 'dateUpdated' => [deep_copy($from), deep_copy($expected)];

        $childChapter = new CodeBook\Chapter();
        $childChapter->setNodeId('childNode');
        $from->addChild($childChapter);

        $expectedChild = new Xml2\Level();
        $expectedChild->setId('childNode');
        $expectedChild->setRole('chapter');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
