<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function implode;
use function is_string;
use function settype;
use function sprintf;

/**
 * @since 1.11
 */
class TableMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'formal-table';
    }

    public static function dtoClass(): string
    {
        return Table::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Table);

        foreach ($attrs as $key => $value) {
            if ('frame' === $key && is_string($value)) {
                if (!in_array($value, Frame::cases(), true)) {
                    $value = Frame::NONE;
                }
                $element->setFrame($value);
            } elseif ('orient' === $key && is_string($value)) {
                if (!in_array($value, Orientation::cases(), true)) {
                    $value = Orientation::PORTRAIT;
                }
                $element->setOrientation($value);
            } elseif ('colsep' === $key && is_string($value)) {
                $element->setColumnSeparator('1' === $value);
            } elseif ('rowsep' === $key && is_string($value)) {
                $element->setRowSeparator('1' === $value);
            } elseif ('float' === $key && is_string($value)) {
                if (!in_array($value, FloatEnum::cases(), true)) {
                    $value = FloatEnum::NONE;
                }
                $element->setFloat($value);
            } elseif ('background-color' === $key && is_string($value)) {
                $element->setBackgroundColor($value);
            } elseif ('tabstyle' === $key && is_string($value)) {
                $element->setTableStyle($value);
            } elseif ('tocentry' === $key && is_string($value)) {
                $element->setTocEntry('yes' === $value);
            } elseif ('pgwide' === $key && is_string($value)) {
                $element->setPageWide('yes' === $value);
            } elseif ('class' === $key && is_string($value)) {
                $element->setClass($value);
            } elseif ('title' === $key && is_string($value)) {
                $element->setTitleAttr($value);
            } elseif ('summary' === $key && is_string($value)) {
                $element->setSummary($value);
            } elseif ('width' === $key && is_string($value)) {
                $element->setWidth($value);
            } elseif ('border' === $key && is_string($value)) {
                $element->setBorder($value);
            } elseif ('cellspacing' === $key && settype($value, 'int')) {
                $element->setCellSpacing($value);
            } elseif ('cellpadding' === $key && settype($value, 'int')) {
                $element->setCellPadding($value);
            } elseif ('rules' === $key && is_string($value)) {
                if (!in_array($value, Rules::cases(), true)) {
                    $value = Rules::NONE;
                }
                $element->setRules($value);
            }
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Table);

        $body = [];
        $legend = [];

        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('QR-code' === $key && $value instanceof QrCode) {
                $element->setqrCode($value);
            } elseif ('table' === $key && is_string($value)) {
                $body[] = $value;
            } elseif (in_array($key, ['mediaobject', 'mediaobject-group'], true)) {
                $body[] = $value;
            } elseif ('caption' === $key && $value instanceof Caption) {
                $element->setCaption($value);
            } elseif ('legend' === $key && is_string($value)) {
                $legend[] = $value;
            } elseif ('table-notes' === $key && $value instanceof TableNotes) {
                $element->setTableNotes($value);
            } elseif ('source' === $key && $value instanceof Source) {
                $element->setSource($value);
            } elseif ('credit' === $key && $value instanceof Credit) {
                $element->setCredit($value);
            }
        }

        $element->setBody(implode("\n", $body));
        $element->setLegend(implode("\n", $legend));
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Table);

        if (!empty($element->getFrame()) && Frame::NONE !== $element->getFrame()) {
            $attrs['frame'] = $element->getFrame();
        }
        if (!empty($element->getOrientation()) && Orientation::PORTRAIT !== $element->getOrientation()) {
            $attrs['orient'] = $element->getOrientation();
        }
        if ($element->isColumnSeparator()) {
            $attrs['colsep'] = '1';
        }
        if ($element->isRowSeparator()) {
            $attrs['rowsep'] = '1';
        }
        if (!empty($element->getFloat())) {
            $attrs['float'] = $element->getFloat();
        }
        if (!empty($element->getBackgroundColor())) {
            $attrs['background-color'] = $element->getBackgroundColor();
        }
        if (!empty($element->getTableStyle())) {
            $attrs['tabstyle'] = $element->getTableStyle();
        }
        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
        if ($element->isPageWide()) {
            $attrs['pgwide'] = 'yes';
        }
        if (!empty($element->getClass())) {
            $attrs['class'] = $element->getClass();
        }
        if (!empty($element->getTitleAttr())) {
            $attrs['title'] = $element->getTitleAttr();
        }
        if (!empty($element->getSummary())) {
            $attrs['summary'] = $element->getSummary();
        }
        if (!empty($element->getWidth())) {
            $attrs['width'] = $element->getWidth();
        }
        if (!empty($element->getBorder())) {
            $attrs['border'] = $element->getBorder();
        }
        if (!empty($element->getCellSpacing())) {
            $attrs['cellspacing'] = sprintf('%d', $element->getCellSpacing());
        }
        if (!empty($element->getCellPadding())) {
            $attrs['cellpadding'] = sprintf('%d', $element->getCellPadding());
        }
        if (!empty($element->getRules()) && Rules::NONE !== $element->getRules()) {
            $attrs['rules'] = $element->getRules();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Table);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if ($element->getQrCode()) {
            $writer->writeElement(self::CLARK . 'QR-code', $element->getQrCode());
        }

        $writer->writeRaw($element->getBody());

        if ($element->getCaption()) {
            $writer->writeElement(self::CLARK . 'caption', $element->getCaption());
        }
        if (!empty($element->getLegend())) {
            $writer->writeRaw($element->getLegend());
        }
        if ($element->getTableNotes()) {
            $writer->writeElement(self::CLARK . 'table-notes', $element->getTableNotes());
        }
        if ($element->getSource()) {
            $writer->writeElement(self::CLARK . 'source', $element->getSource());
        }
        if ($element->getCredit()) {
            $writer->writeElement(self::CLARK . 'credit', $element->getCredit());
        }
    }
}
