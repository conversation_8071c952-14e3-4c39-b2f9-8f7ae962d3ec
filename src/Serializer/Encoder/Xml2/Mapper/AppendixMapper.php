<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function in_array;
use function is_string;

class AppendixMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'appendix';
    }

    public static function dtoClass(): string
    {
        return Appendix::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Appendix);

        if (array_key_exists('indexnum', $attrs) && is_string($attrs['indexnum'])) {
            $element->setIndexNumber($attrs['indexnum']);
        }
        if (array_key_exists('tocentry', $attrs)) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
        if (array_key_exists('tocautoadd', $attrs)) {
            $element->setTocAutoAdd('yes' === $attrs['tocautoadd']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Appendix);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('metadata' === $key && $value instanceof Metadata) {
                $element->setMetadata($value);
            } elseif ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('QR-code' === $key && $value instanceof QrCode) {
                $element->setQrCode($value);
            } elseif ('history' === $key && $value instanceof History) {
                $element->setHistory($value);
            } elseif ('objectives' === $key && $value instanceof Objectives) {
                $element->setObjectives($value);
            } elseif ('note' === $key && $value instanceof Note) {
                $element->setNote($value);
            } elseif ('abstract' === $key && $value instanceof AbstractField) {
                $element->setAbstract($value);
            } elseif ('keywords' === $key && $value instanceof Keywords) {
                $element->setKeywords($value);
            } elseif ('section-body' === $key && $value instanceof SectionBody) {
                $element->setBody($value);
            } elseif (in_array($key, ['level-2', 'section', 'relocated-to'], true)) {
                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Appendix);

        if (!empty($element->getIndexNumber())) {
            $attrs['indexnum'] = $element->getIndexNumber();
        }
        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
        if ($element->isTocAutoAdd()) {
            $attrs['tocautoadd'] = 'yes';
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Appendix);

        if ($element->getMetadata()) {
            $writer->writeElement(self::CLARK . 'metadata', $element->getMetadata());
        }
        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if ($element->getQrCode()) {
            $writer->writeElement(self::CLARK . 'QR-code', $element->getQrCode());
        }
        if ($element->getHistory()) {
            $writer->writeElement(self::CLARK . 'history', $element->getHistory());
        }
        if ($element->getObjectives()) {
            $writer->writeElement(self::CLARK . 'objectives', $element->getObjectives());
        }
        if ($element->getNote()) {
            $writer->writeElement(self::CLARK . 'note', $element->getNote());
        }
        if ($element->getAbstract()) {
            $writer->writeElement(self::CLARK . 'abstract', $element->getAbstract());
        }
        if ($element->getKeywords()) {
            $writer->writeElement(self::CLARK . 'keywords', $element->getKeywords());
        }
        if ($element->getBody()) {
            $writer->writeElement(self::CLARK . 'section-body', $element->getBody());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Level) {
                $i->setDisplayLevel(2);
                $writer->writeElement(self::CLARK . 'level-2', $i);
            } elseif ($i instanceof Section) {
                $writer->writeElement(self::CLARK . 'section', $i);
            } elseif ($i instanceof RelocatedTo) {
                $writer->writeElement(self::CLARK . 'relocated-to', $i);
            }
        }
    }
}
