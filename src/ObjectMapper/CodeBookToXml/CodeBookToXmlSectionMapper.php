<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlSectionMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Section $from): Xml2\Section
    {
        $to = new Xml2\Section();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        $to->setIndexNumber($from->getIndexNumber());
        $to->setTocEntry($from->hasTocEntry());
        $to->setReserveCount($from->getReserveCount());
        $to->setDisplayLevel($from->getDisplayLevel());

        if (!empty($from->getBody())) {
            $body = new Xml2\SectionBody();
            $body->setBody($from->getBody());
            $to->setBody($body);
        }

        $to->setAbstract($this->createAbstract($from->getAbstractTitle(), $from->getAbstract()));
        $to->setKeywords($this->createKeywords($from->getKeywordsTitle(), $from->getKeywords()));

        $children = [];
        foreach ($from->getChildren() as $child) {
            $children[] = $this->mapper->map($child);
        }
        $to->setChildren($children);

        return $to;
    }
}
