<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Command\CodeBook;

use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\PublicationRefLink;
use App\Helper\Xml2Helper;
use App\Message\CodeBook\UpsertPublicationRefLinksAsyncMessage;
use App\MessageHandler\CodeBook\UpsertPublicationRefLinksAsyncMessageHandler;
use App\Service\Helper\PhpHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

use function iterator_to_array;
use function sprintf;

#[AsCommand(name: 'app:code-book:upsert-publication-ref-links')]
class UpsertPublicationRefLinksCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface                        $em,
        private readonly MessageBusInterface                           $messageBus,
        private readonly UpsertPublicationRefLinksAsyncMessageHandler $handler,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        PhpHelper::setUnlimitedLimits();

        // Check if we need to do initial population
        $existingLinksCount = $this->em->getRepository(PublicationRefLink::class)->count([]);

        if ($existingLinksCount === 0) {
            $output->writeln('No existing PublicationRefLink entities found. Running initial population...');
            $this->initialPopulation($output);
        } else {
            $output->writeln(sprintf('Found %d existing PublicationRefLink entities. Updating them...', $existingLinksCount));
            $this->updateExistingLinks($output);
        }

        $this->em->flush();

        return Command::SUCCESS;
    }

    private function initialPopulation(OutputInterface $output): void
    {
        $sections = $this->em->getRepository(Section::class)
            ->createQueryBuilder('s')
            ->where('s.body LIKE :pattern')
            ->setParameter('pattern', '%<publication-ref %')
            ->getQuery()
            ->getResult();

        $output->writeln(sprintf('Found %d sections containing publication-ref elements...', count($sections)));

        $totalProcessed = 0;
        foreach ($sections as $section) {
            $this->messageBus->dispatch(new UpsertPublicationRefLinksAsyncMessage($section));
            $totalProcessed++;

            if ($totalProcessed % 100 === 0) {
                $this->em->clear();
                $output->writeln(sprintf('Dispatched %d messages...', $totalProcessed));
            }
        }

        $output->writeln(sprintf('Initial population complete. Dispatched %d messages for processing.', $totalProcessed));
    }

    private function updateExistingLinks(OutputInterface $output): void
    {
        $parseBodyMethod = new \ReflectionMethod($this->handler, 'parseBody');

        $publicationRefLinks = $this->em->getRepository(PublicationRefLink::class)->findAll();
        foreach ($publicationRefLinks as $publicationRefLink) {
            $fromNode = $publicationRefLink->getFromNode();
            if ($fromNode instanceof Section) {
                $document = Xml2Helper::createDOMDocument($fromNode->getBody(), true);
                $xpath = Xml2Helper::createDOMXPath($document);

                $linkNodes = $xpath->query(sprintf('//x:publication-ref[@ct-uuid="%s"]', $publicationRefLink->getUuid()), $document->documentElement);
                /** @var \DOMElement[] $arr */
                $arr = iterator_to_array($linkNodes);
                foreach ($arr as $domEl) {
                    while (null !== $domEl->firstChild) {
                        $domEl->removeChild($domEl->firstChild);
                    }

                    if (!empty($publicationRefLink->getOriginalBody())) {
                        $frag = $domEl->ownerDocument->createDocumentFragment();
                        $frag->appendXML($publicationRefLink->getOriginalBody());
                        $domEl->appendChild($frag);

                        $parseBodyMethod->invoke($this->handler, $publicationRefLink, $domEl);
                    }
                }

                $body = Xml2Helper::getInnerXml($document->documentElement);
                $fromNode->setBody($body);

                $this->em->persist($fromNode);
                $this->em->persist($publicationRefLink);
            }
        }

        $output->writeln(sprintf('Updated %d existing PublicationRefLink entities.', count($publicationRefLinks)));
    }
}
