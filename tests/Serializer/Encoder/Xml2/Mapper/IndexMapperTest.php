<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\IndexMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\IndexMapper
 */
class IndexMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Index::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Index::class]);
        $this->assertInstanceOf(IndexMapper::class, $service->classMap[Index::class][0]);

        $this->assertArrayHasKey(IndexMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[IndexMapper::elementName()]);
        $this->assertInstanceOf(IndexMapper::class, $service->elementMap[IndexMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Index $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<index %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Index();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Index $element, string $innerXml): void
    {
        $xml = sprintf('<index xmlns="%s" xmlns:m="%s">%s</index>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Index();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setChildren([new IndexDivision()]);
        $innerXml .= '<indexdiv/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $element->setChildren([new IndexDivision(), new IndexEntry()]);
        $innerXml .= '<index-entry/>';
        yield '2 children' => [deep_copy($element), $innerXml];
    }
}
