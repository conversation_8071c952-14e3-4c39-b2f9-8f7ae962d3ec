<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Table extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\CalsTableAttributes;
    use Attribute\HtmlTableAttributes;

    use Field\TitleGroupTrait;
    use Field\QrCodeTrait;

    public string $body = '';
    public ?Caption $caption = null;
    public string $legend = '';
    public ?TableNotes $tableNotes = null;
    public ?Source $source = null;
    public ?Credit $credit = null;

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): void
    {
        $this->body = $body;
    }

    public function getCaption(): ?Caption
    {
        return $this->caption;
    }

    public function setCaption(?Caption $caption): void
    {
        $this->caption = $caption;
    }

    public function getLegend(): string
    {
        return $this->legend;
    }

    public function setLegend(string $legend): void
    {
        $this->legend = $legend;
    }

    public function getTableNotes(): ?TableNotes
    {
        return $this->tableNotes;
    }

    public function setTableNotes(?TableNotes $tableNotes): void
    {
        $this->tableNotes = $tableNotes;
    }

    public function getSource(): ?Source
    {
        return $this->source;
    }

    public function setSource(?Source $source): void
    {
        $this->source = $source;
    }

    public function getCredit(): ?Credit
    {
        return $this->credit;
    }

    public function setCredit(?Credit $credit): void
    {
        $this->credit = $credit;
    }
}
