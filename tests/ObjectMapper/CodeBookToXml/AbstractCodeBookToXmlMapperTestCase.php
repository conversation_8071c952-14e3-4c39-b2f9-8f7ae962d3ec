<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CommonBook\Attribute\RevisionAttributes as CodeBookRevisionAttributes;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\Appendix as Xml2Appendix;
use App\Serializer\Encoder\Xml2\Element\Attribute\CommonAttributes as Xml2CommonAttributes;
use App\Serializer\Encoder\Xml2\Element\Attribute\RevisionAttributes as Xml2RevisionAttributes;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\Field\QrCodeTrait;
use App\Serializer\Encoder\Xml2\Element\Field\TitleGroupTrait;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\ShortCode;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

abstract class AbstractCodeBookToXmlMapperTestCase extends KernelTestCase
{
    protected function commonAttributeMapCases(AbstractCodeBookNode $from, AbstractElement $expected): iterable
    {
        $from->setNodeId('nodeId');
        $expected->setId('nodeId');
        yield '@id' => [deep_copy($from), deep_copy($expected)];

        /** @var Xml2CommonAttributes $expected */
        $from->setRole('role');
        $expected->setRole('role');
        yield 'common attrs / @role' => [deep_copy($from), deep_copy($expected)];

        $from->setDisplay('display');
        $expected->setDisplay('display');
        yield 'common attrs / @display' => [deep_copy($from), deep_copy($expected)];

        $from->setVerbatim(true);
        $expected->setVerbatim(true);
        yield 'common attrs / @verbatim' => [deep_copy($from), deep_copy($expected)];

        $from->setLanguage('language');
        $expected->setLanguage('language');
        yield 'common attrs / @language' => [deep_copy($from), deep_copy($expected)];

        $from->setAdditionalInfo('add info');
        $expected->setAdditionalInfo('add info');
        yield 'common attrs / @additionalInfo' => [deep_copy($from), deep_copy($expected)];
    }

    protected function revisionAttributeMapCases(AbstractCodeBookNode $from, AbstractElement $expected): iterable
    {
        /** @var CodeBookRevisionAttributes $from */
        /** @var Xml2RevisionAttributes $expected */

        $from->setRevisionBy('revisionBy');
        $expected->setRevisionBy('revisionBy');
        yield 'revision attrs / @revisionBy' => [deep_copy($from), deep_copy($expected)];

        $dt = new DateTime();
        $from->setRevisionDateTime($dt);
        $expected->setRevisionDateTime($dt);
        yield 'revision attrs / @revisionDateTime' => [deep_copy($from), deep_copy($expected)];

        $from->setRevision('revision');
        $expected->setRevision('revision');
        yield 'revision attrs / @revision' => [deep_copy($from), deep_copy($expected)];

        $from->setRevisionGroup('revisionGroup');
        $expected->setRevisionGroup('revisionGroup');
        yield 'revision attrs / @revisionGroup' => [deep_copy($from), deep_copy($expected)];

        $from->setDataChanged('dataChanged');
        $expected->setDataChanged('dataChanged');
        yield 'revision attrs / @dataChanged' => [deep_copy($from), deep_copy($expected)];

        $from->setDataChangedIn('dataChangedIn');
        $expected->setDataChangedIn('dataChangedIn');
        yield 'revision attrs / @dataChangedIn' => [deep_copy($from), deep_copy($expected)];

        $from->setRelocatedFromAttr('relocatedFrom');
        $expected->setRelocatedFromAttr('relocatedFrom');
        yield 'revision attrs / @relocatedFromAttr' => [deep_copy($from), deep_copy($expected)];
    }

    protected function titleGroupMapCases(AbstractCodeBookNode $from, AbstractElement $expected): iterable
    {
        yield 'titleGroup / null' => [deep_copy($from), deep_copy($expected)];

        /** @var \App\Entity\CommonBook\Field\TitleGroup $from */
        /** @var TitleGroupTrait $expected */

        $titleGroup = new TitleGroup();
        $expected->setTitleGroup($titleGroup);

        $from->setSuperTitle('superTitle');
        $superTitle = new SuperTitle();
        $superTitle->setBody('superTitle');
        $titleGroup->setSuperTitle($superTitle);
        yield 'titleGroup / superTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setCommitteeDesignation('committeeDesignation');
        $cd = new CommitteeDesignation();
        $cd->setBody('committeeDesignation');
        $titleGroup->setCommitteeDesignation($cd);
        yield 'titleGroup / committeeDesignation' => [deep_copy($from), deep_copy($expected)];

        $from->setLabel('label');
        $label = new Label();
        $label->setBody('label');
        $titleGroup->setLabel($label);
        yield 'titleGroup / label' => [deep_copy($from), deep_copy($expected)];

        $from->setNumber('number');
        $number = new Number();
        $number->setBody('number');
        $titleGroup->setNumber($number);
        yield 'titleGroup / number' => [deep_copy($from), deep_copy($expected)];

        $from->setCorrelated('correlated');
        $correlated = new Correlated();
        $correlated->setBody('correlated');
        $titleGroup->setCorrelated($correlated);
        yield 'titleGroup / correlated' => [deep_copy($from), deep_copy($expected)];

        $from->setTitle('title');
        $title = new Title();
        $title->setBody('title');
        $titleGroup->setTitle($title);
        yield 'titleGroup / title' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleAbbreviation('titleAbbreviation');
        $title->setTitleAbbreviation('titleAbbreviation');
        yield 'titleGroup / titleAbbreviation' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleYear('titleYear');
        $title->setTitleYear('titleYear');
        yield 'titleGroup / titleYear' => [deep_copy($from), deep_copy($expected)];

        $from->setSubTitle('subTitle');
        $subTitle = new SubTitle();
        $subTitle->setBody('subTitle');
        $titleGroup->setSubTitle($subTitle);
        yield 'titleGroup / subTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleHistory('titleHistory');
        $history = new History();
        $history->setBody('titleHistory');
        $titleGroup->setHistory($history);
        yield 'titleGroup / titleHistory' => [deep_copy($from), deep_copy($expected)];
    }

    protected function qrCodeMapCases(AbstractCodeBookNode $from, AbstractElement $expected): iterable
    {
        yield 'qrCode / null' => [deep_copy($from), deep_copy($expected)];

        /** @var \App\Entity\CommonBook\Field\QrCode $from */
        /** @var QrCodeTrait $expected */

        $qrCode = new QrCode();
        $shortCode = new ShortCode();
        $qrCode->setShortCode($shortCode);
        $expected->setQrCode($qrCode);
        $from->setQrActive(true);

        $from->setQrId('qrId');
        $qrCode->setId('qrId');
        yield 'qrCode / qrId' => [deep_copy($from), deep_copy($expected)];

        $from->setQrDisplay(true);
        $qrCode->setQrDisplay(true);
        yield 'qrCode / qrDisplay' => [deep_copy($from), deep_copy($expected)];

        $from->setQrLevelReference('qrLevelReference');
        $qrCode->setLevelReference('qrLevelReference');
        yield 'qrCode / qrLevelReference' => [deep_copy($from), deep_copy($expected)];

        $from->setQrPurpose('qrPurpose');
        $qrCode->setPurpose('qrPurpose');
        yield 'qrCode / qrPurpose' => [deep_copy($from), deep_copy($expected)];

        $from->setQrImage('qrImage');
        $qrCode->setImg('qrImage');
        yield 'qrCode / qrImage' => [deep_copy($from), deep_copy($expected)];

        $from->setQrShortUrl('qrShortUrl');
        $shortCode->setShortUrl('qrShortUrl');
        yield 'qrCode / qrShortUrl' => [deep_copy($from), deep_copy($expected)];

        $from->setQrBookIcon('qrBookIcon');
        $qrCode->setBookIcon('qrBookIcon');
        yield 'qrCode / qrBookIcon' => [deep_copy($from), deep_copy($expected)];
    }
}
