<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookTertiaryIndexEntryMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookTertiaryIndexEntryMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookTertiaryIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookTertiaryIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\TertiaryIndexEntry $fromNode, CodeBook\TertiaryIndexEntry $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\TertiaryIndexEntry();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\TertiaryIndexEntry();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setReferenceId('referenceId');
        $expected->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTerm('term');
        $expected->setTerm('term');
        yield 'term' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setNavPointerGroup('navPointerGroup');
        $expected->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
