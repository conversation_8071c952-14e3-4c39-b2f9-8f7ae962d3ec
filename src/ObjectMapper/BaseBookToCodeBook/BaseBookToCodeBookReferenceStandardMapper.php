<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookReferenceStandardMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\ReferenceStandard $from): CodeBook\ReferenceStandard
    {
        $to = new CodeBook\ReferenceStandard();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setNumber($from->getNumber());
        $to->setTitle($from->getTitle());
        $to->setNavPointerGroup($from->getNavPointerGroup());

        return $to;
    }
}
