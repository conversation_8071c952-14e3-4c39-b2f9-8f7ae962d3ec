<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Meta;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class MetaMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'meta';
    }

    public static function dtoClass(): string
    {
        return Meta::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Meta);

        if (array_key_exists('name', $attrs) && is_string($attrs['name'])) {
            $element->setName($attrs['name']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Meta);
        $attrs['name'] = $element->getName();
    }
}
