<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use function settype;

/**
 * @since 1.11
 */
trait DisplayLevel
{
    public int $displayLevel = 1;

    public function getDisplayLevel(): int
    {
        return $this->displayLevel;
    }

    /** @param string|int $displayLevel */
    public function setDisplayLevel($displayLevel): void
    {
        if (settype($displayLevel, 'integer')) {
            $this->displayLevel = (int) $displayLevel;
        }
    }
}
