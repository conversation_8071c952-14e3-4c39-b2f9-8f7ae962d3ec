<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\Attribute\CommonAttributes;
use App\Serializer\Encoder\Xml2\Element\Attribute\ErrataAttributes;
use App\Serializer\Encoder\Xml2\Element\Attribute\RevisionAttributes;
use App\Serializer\Encoder\Xml2\Xml2Service;
use DateTime;
use DOMDocument;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;
use function implode;
use function sprintf;

abstract class MapperTestCase extends KernelTestCase
{
    protected Xml2Service $service;
    protected string $schemaFile = '';

    protected function setUp(): void
    {
        $this->service = self::getContainer()->get(Xml2Service::class);
        $this->schemaFile = self::getContainer()->getParameter('xml.schema');
    }

    protected function assertValidSchema(string $xml): void
    {
        $document = new DOMDocument();
        $document->loadXML($xml);
        $this->assertTrue($document->schemaValidate($this->schemaFile));
    }

    protected function arrayToAttrs(array $attrs): string
    {
        $vals = [];
        foreach ($attrs as $key => $value) {
            $vals[] = sprintf('%s="%s"', $key, $value);
        }
        return implode(' ', $vals);
    }

    protected function commonAttributeCases(AbstractElement $element, array &$attrs): iterable
    {
        $element->setId('elementId');
        $attrs['id'] = 'elementId';
        yield 'common attrs / @id' => [deep_copy($element), $attrs];

        /** @var CommonAttributes $element */
        $element->setRole('role');
        $attrs['role'] = 'role';
        yield 'common attrs / @role' => [deep_copy($element), $attrs];

        $element->setDisplay('display');
        $attrs['display'] = 'display';
        yield 'common attrs / @display' => [deep_copy($element), $attrs];

        $element->setVerbatim(true);
        $attrs['verbatim'] = 'yes';
        yield 'common attrs / @verbatim = yes' => [deep_copy($element), $attrs];

        $element->setVerbatim(false);
        unset($attrs['verbatim']);
        yield 'common attrs / @verbatim = no' => [deep_copy($element), $attrs];

        $element->setLanguage('lang');
        $attrs['lang'] = 'lang';
        yield 'common attrs / @lang' => [deep_copy($element), $attrs];

        $element->setAdditionalInfo('add-info');
        $attrs['add-info'] = 'add-info';
        yield 'common attrs / @add-info' => [deep_copy($element), $attrs];
    }

    protected function revisionAttributeCases(AbstractElement $element, array &$attrs): iterable
    {
        /** @var RevisionAttributes $element */
        $element->setRevisionBy('revision-by');
        $attrs['revision-by'] = 'revision-by';
        yield 'revision attr / revision-by' => [deep_copy($element), $attrs];

        $element->setRevisionDateTime(new DateTime('2024-01-01T00:00:00+00:00'));
        $attrs['revision-datetime'] = '2024-01-01T00:00:00+00:00';
        yield 'revision attr / revision-datetime' => [deep_copy($element), $attrs];

        $element->setRevision('added');
        $attrs['revision'] = 'added';
        yield 'revision attr / revision' => [deep_copy($element), $attrs];

        $element->setRevisionGroup('revision-group');
        $attrs['revision-group'] = 'revision-group';
        yield 'revision attr / revision-group' => [deep_copy($element), $attrs];

        $element->setDataChanged('data-changed');
        $attrs['data-changed'] = 'data-changed';
        yield 'revision attr / data-changed' => [deep_copy($element), $attrs];

        $element->setDataChangedIn('data-changed-in');
        $attrs['data-changed-in'] = 'data-changed-in';
        yield 'revision attr / data-changed-in' => [deep_copy($element), $attrs];

        $element->setRelocatedFromAttr('id');
        $attrs['relocated-from'] = 'id';
        yield 'revision attr / relocated-from' => [deep_copy($element), $attrs];
    }

    protected function errataAttributeCases(AbstractElement $element, array &$attrs): iterable
    {
        /** @var ErrataAttributes $element */
        $element->setErrata('errata');
        $attrs['errata'] = 'errata';
        yield 'errata attr / errata' => [deep_copy($element), $attrs];

        $element->setErrataList('errata list');
        $attrs['errata-list'] = 'errata list';
        yield 'errata attr / errata-list' => [deep_copy($element), $attrs];
    }
}
