<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Enum\FloatEnum;

/**
 * @since 1.11
 */
trait FloatAttr
{
    public string $float = FloatEnum::NONE;

    public function getFloat(): string
    {
        return $this->float;
    }

    public function setFloat(string $float): void
    {
        $this->float = $float;
    }
}
