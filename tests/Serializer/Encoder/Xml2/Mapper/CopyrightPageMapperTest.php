<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\CopyrightPageMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CopyrightPageMapper
 */
class CopyrightPageMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(CopyrightPage::class, $service->classMap);
        $this->assertIsCallable($service->classMap[CopyrightPage::class]);
        $this->assertInstanceOf(CopyrightPageMapper::class, $service->classMap[CopyrightPage::class][0]);

        $this->assertArrayHasKey(CopyrightPageMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CopyrightPageMapper::elementName()]);
        $this->assertInstanceOf(CopyrightPageMapper::class, $service->elementMap[CopyrightPageMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(CopyrightPage $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<copyright-page %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new CopyrightPage();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(CopyrightPage $element, string $innerXml): void
    {
        $xml = sprintf('<copyright-page xmlns="%s" xmlns:m="%s">%s</copyright-page>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new CopyrightPage();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $body = <<<XMLFRAG
<publication-history type="print">
  <p>First Printing: August 2023</p>
  <p>Second Printing: August 2024</p>
</publication-history>
<isbn role="soft-cover">ISBN: 978-1-959851-60-8 (soft-cover edition)</isbn>
<isbn role="loose-leaf">ISBN: 978-1-959851-61-5 (loose-leaf edition)</isbn>
<isbn role="pdf-download">ISBN: 978-1-959851-62-2 (PDF download)</isbn>
<copyright year="2023" holder="International Code Council, Inc">
  <copyright-statement>COPYRIGHT © 2023 by INTERNATIONAL CODE COUNCIL, INC.</copyright-statement>
</copyright>
<legalnotice>
  <p>ALL RIGHTS RESERVED. This 2024 <emphasis>International Building Code</emphasis><sup>®</sup> is a copyrighted work owned by the International Code Council, Inc. (“ICC”). Without separate written permission from the ICC, no part of
    this publication may be reproduced, distributed or transmitted in any form or by any means, including, without limitation, electronic, optical or mechanical means (by way of example, and not limitation, photocopying or recording by or in
    an information storage and/or retrieval system). For information on use rights and permissions, please contact: ICC Publications, 4051 Flossmoor Road, Country Club Hills, Illinois 60478; 1-888-ICC-SAFE (422-7233);
    <url href="https://www.iccsafe.org/about/periodicals-and-newsroom/icc-logo-license/">https://www.iccsafe.org/about/periodicals-and-newsroom/icc-logo-license/</url>.</p>
</legalnotice>
<trademarks>
  <p>Trademarks: “International Code Council,” the “International Code Council” logo, “ICC,” the “ICC” logo, “International Building Code,” “IBC” and other names and trademarks appearing in this publication are registered trademarks of the
    International Code Council, Inc., and/or its licensors (as applicable), and may not be used without permission.</p>
</trademarks>
<printing-location>
  <p>PRINTED IN THE USA</p>
</printing-location>
XMLFRAG;
        $element->setBody($body);
        $innerXml .= $body;
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
