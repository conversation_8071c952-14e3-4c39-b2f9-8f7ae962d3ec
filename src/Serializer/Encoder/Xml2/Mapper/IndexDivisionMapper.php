<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;

class IndexDivisionMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'indexdiv';
    }

    public static function dtoClass(): string
    {
        return IndexDivision::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof IndexDivision);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('index-entry' === $key && $value instanceof IndexEntry) {
                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof IndexDivision);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof IndexEntry) {
                $writer->writeElement(self::CLARK . 'index-entry', $i);
            }
        }
    }
}
