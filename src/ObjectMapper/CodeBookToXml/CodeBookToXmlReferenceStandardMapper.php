<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlReferenceStandardMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\ReferenceStandard $from): Xml2\Reference
    {
        $to = new Xml2\Reference();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);

        $titleGroup = new Xml2\TitleGroup();
        $to->setTitleGroup($titleGroup);

        if (!empty($from->getNumber())) {
            $number = new Xml2\Number();
            $number->setBody($from->getNumber());
            $titleGroup->setNumber($number);
        }

        if (!empty($from->getTitle())) {
            $title = new Xml2\Title();
            $title->setBody($from->getTitle());
            $titleGroup->setTitle($title);
        }

        if (!empty($from->getNavPointerGroup())) {
            $to->setNavPointerGroup($from->getNavPointerGroup());
        }

        return $to;
    }
}
