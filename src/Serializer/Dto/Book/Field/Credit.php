<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

trait Credit
{
    public string $credit = '';
    public string $creditTitle = '';

    public function getCredit(): string
    {
        return $this->credit;
    }

    public function setCredit(string $credit): void
    {
        $this->credit = $credit;
    }

    public function getCreditTitle(): string
    {
        return $this->creditTitle;
    }

    public function setCreditTitle(string $creditTitle): void
    {
        $this->creditTitle = $creditTitle;
    }
}
