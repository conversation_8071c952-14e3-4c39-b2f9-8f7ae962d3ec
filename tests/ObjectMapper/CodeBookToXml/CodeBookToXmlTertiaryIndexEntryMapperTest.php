<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTertiaryIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlTertiaryIndexEntryMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlTertiaryIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlTertiaryIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\TertiaryIndexEntry $from, Xml2\TertiaryIndexEntry $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\TertiaryIndexEntry();
        $from->setNodeId('');
        $expected = new Xml2\TertiaryIndexEntry();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);

        $from->setReferenceId('referenceId');
        $expected->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($from), deep_copy($expected)];

        $from->setTerm('term');
        $term = new Xml2\Term();
        $term->setBody('term');
        $expected->setTerm($term);
        yield 'term' => [deep_copy($from), deep_copy($expected)];

        $from->setNavPointerGroup('navPointerGroup');
        $expected->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($from), deep_copy($expected)];
    }
}
