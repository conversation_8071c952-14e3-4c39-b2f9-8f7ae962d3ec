<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookDefinitionListMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookDefinitionListMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookDefinitionListMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\DefinitionList $fromNode, CodeBook\DefinitionList $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\DefinitionList();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\DefinitionList();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        $expected->setRole('');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setChildren([new BaseBook\Definition(), new BaseBook\Definition(), new BaseBook\RelocatedFrom(), new BaseBook\RelocatedTo()]);
        $expected->setChildren([new CodeBook\Definition(), new CodeBook\Definition(), new CodeBook\RelocatedFrom(), new CodeBook\RelocatedTo()]);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
