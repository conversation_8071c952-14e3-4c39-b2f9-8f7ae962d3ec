<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @since 1.11
 */
trait CommitteeDesignation
{
    #[Groups(['list:contents'])]
    public string $committeeDesignation = '';

    public function getCommitteeDesignation(): string
    {
        return $this->committeeDesignation;
    }

    public function setCommitteeDesignation(string $committeeDesignation): void
    {
        $this->committeeDesignation = $committeeDesignation;
    }
}
