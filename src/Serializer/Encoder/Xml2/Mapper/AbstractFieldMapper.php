<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractBlockElement;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function is_string;
use function mb_trim;

/**
 * @since 1.11
 */
class AbstractFieldMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'abstract';
    }

    public static function dtoClass(): string
    {
        return AbstractField::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof AbstractField);

        $body = '';
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif (is_string($value)) {
                $body .= $value . "\n";
            } elseif ($value instanceof AbstractBlockElement) {
                $body .= $value->getOuterXml() . "\n";
            }
        }

        $element->setBody(mb_trim($body));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof AbstractField);

        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        if (!empty($element->getBody())) {
            $writer->writeRaw($element->getBody());
        }
    }
}
