<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Volume;
use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Serializer\Normalizer\CodeBookToArray\FigureToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class FigureToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?FigureToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(FigureToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new Figure(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new Figure(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new Figure()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Figure $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new Figure();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $expected = [
            '_type'                => 'figure',
            'dataType'             => 'figure',

            // attributes
            'id'                   => 'nodeId',
            'uuid'                 => 'uuid',
            'ctXmlId'              => 'nodeId',
            'role'                 => '',
            'display'              => '',
            'verbatim'             => false,
            'language'             => '',
            'additionalInfo'       => '',
            'revisionBy'           => '',
            'revisionDateTime'     => null,
            'revision'             => '',
            'revisionGroup'        => '',
            'dataChanged'          => '',
            'dataChangedIn'        => '',
            'relocatedFromAttr'    => '',
            'deletedBy'            => '',
            'deletedDate'          => null,
            'tocEntry'             => false,
            'float'                => '',
            'orientation'          => Orientation::PORTRAIT,

            // title group
            'superTitle'           => '',
            'committeeDesignation' => '',
            'label'                => '',
            'number'               => '',
            'correlated'           => '',
            'title'                => '',
            'titleAbbreviation'    => '',
            'titleYear'            => '',
            'subTitle'             => '',

            // qr code
            'qrActive'             => false,
            'qrId'                 => 'nodeId',
            'qrDisplay'            => false,
            'qrLevelReference'     => '',
            'qrPurpose'            => '',
            'qrImage'              => '',
            'qrShortUrl'           => '',
            'qrBookIcon'           => '',
            'qrUrl'                => '',
            'qrIcon'               => '',
            'qrDepartment'         => QrDepartment::DEFAULT,
            'qrBusinessUnit'       => QrBusinessUnit::DEFAULT,

            // fields
            'media'                => '',
            'caption'              => '',
            'figureNotesTitle'     => '',
            'figureNotes'          => '',
            'legend'               => '',
            'source'               => '',
            'creditTitle'          => '',
            'credit'               => '',
            'qaCheck'              => '',
            'imageSrc'             => '',
            'imageAlt'             => '',

            // internal
            'status'               => 'IN_PROGRESS',
            'showDeletionMarker'   => false,
            'codesNotes'           => '',
            'pubsNotes'            => '',
            'typesetterNotes'      => '',
            'hasCodesNotes'        => false,
            'hasPubsNotes'         => false,
            'hasTypesetterNotes'   => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected['tocEntry'] = true;

        yield 'tocEntry' => [deep_copy($from), $expected];

        $from->setFloat(FloatEnum::MARGIN);
        $expected['float'] = FloatEnum::MARGIN;

        yield 'float' => [deep_copy($from), $expected];

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected['orientation'] = Orientation::LANDSCAPE;

        yield 'orientation' => [deep_copy($from), $expected];

        $from->setMedia('media');
        $expected['media'] = 'media';

        yield 'media' => [deep_copy($from), $expected];

        $from->setCaption('caption');
        $expected['caption'] = 'caption';

        yield 'caption' => [deep_copy($from), $expected];

        $from->setFigureNotesTitle('figureNotesTitle');
        $expected['figureNotesTitle'] = 'figureNotesTitle';

        yield 'figureNotesTitle' => [deep_copy($from), $expected];

        $from->setFigureNotes('figureNotes');
        $expected['figureNotes'] = 'figureNotes';

        yield 'figureNotes' => [deep_copy($from), $expected];

        $from->setLegend('legend');
        $expected['legend'] = 'legend';

        yield 'legend' => [deep_copy($from), $expected];

        $from->setSource('source');
        $expected['source'] = 'source';

        yield 'source' => [deep_copy($from), $expected];

        $from->setCreditTitle('creditTitle');
        $expected['creditTitle'] = 'creditTitle';

        yield 'creditTitle' => [deep_copy($from), $expected];

        $from->setCredit('credit');
        $expected['credit'] = 'credit';

        yield 'credit' => [deep_copy($from), $expected];

        $from->setQaCheck(true);
        $expected['qaCheck'] = true;

        yield 'qaCheck' => [deep_copy($from), $expected];
    }
}
