<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Enum\Orientation as OrientationEnum;

/**
 * @since 1.11
 */
trait Orientation
{
    public string $orientation = OrientationEnum::PORTRAIT;

    public function getOrientation(): string
    {
        return $this->orientation;
    }

    public function setOrientation(string $orientation): void
    {
        $this->orientation = $orientation;
    }
}
