<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Acronym;
use App\Serializer\Encoder\Xml2\Mapper\AcronymMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AcronymMapper
 */
class AcronymMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Acronym::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Acronym::class]);
        $this->assertInstanceOf(AcronymMapper::class, $service->classMap[Acronym::class][0]);

        $this->assertArrayHasKey(AcronymMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[AcronymMapper::elementName()]);
        $this->assertInstanceOf(AcronymMapper::class, $service->elementMap[AcronymMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Acronym $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<acronym %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Acronym();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Acronym $element, string $innerXml): void
    {
        $xml = sprintf('<acronym xmlns="%s" xmlns:m="%s">%s</acronym>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Acronym();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<url href="https://iccsafe.org">ICC</url>');
        $innerXml .= '<url href="https://iccsafe.org">ICC</url>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
