<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\FigureNotes;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function in_array;
use function is_string;
use function trim;

/**
 * @since 1.11
 */
class FigureMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'figure';
    }

    public static function dtoClass(): string
    {
        return Figure::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Figure);

        foreach ($attrs as $key => $value) {
            if ('tocentry' === $key) {
                $element->setTocEntry('yes' === $value);
            } elseif ('float' === $key && is_string($value) && in_array($value, FloatEnum::cases(), true)) {
                $element->setFloat($value);
            } elseif ('orient' === $key && is_string($value) && in_array($value, Orientation::cases(), true)) {
                $element->setOrientation($value);
            }
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Figure);

        $legend = '';
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('mediaobject' === $key && is_string($value)) {
                $element->setMedia(trim($value));
            } elseif ('mediaobject-group' === $key && is_string($value)) {
                $element->setMedia(trim($value));
            } elseif ('caption' === $key && $value instanceof Caption) {
                $element->setCaption($value);
            } elseif ('figure-notes' === $key && $value instanceof FigureNotes) {
                $element->setFigureNotes($value);
            } elseif ('legend' === $key && is_string($value)) {
                $legend .= $value . "\n";
            } elseif ('source' === $key && $value instanceof Source) {
                $element->setSource($value);
            } elseif ('credit' === $key && $value instanceof Credit) {
                $element->setCredit($value);
            }
        }

        $element->setLegend(trim($legend));
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Figure);

        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
        if (!empty($element->getFloat())) {
            $attrs['float'] = $element->getFloat();
        }
        if ('land' === $element->getOrientation()) {
            $attrs['orient'] = 'land';
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Figure);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if (!empty($element->getMedia())) {
            $writer->writeRaw($element->getMedia());
        }
        if ($element->getCaption()) {
            $writer->writeElement(self::CLARK . 'caption', $element->getCaption());
        }
        if ($element->getFigureNotes()) {
            $writer->writeElement(self::CLARK . 'figure-notes', $element->getFigureNotes());
        }
        if (!empty($element->getLegend())) {
            $writer->writeRaw($element->getLegend());
        }
        if ($element->getSource()) {
            $writer->writeElement(self::CLARK . 'source', $element->getSource());
        }
        if ($element->getCredit()) {
            $writer->writeElement(self::CLARK . 'credit', $element->getCredit());
        }
    }
}
