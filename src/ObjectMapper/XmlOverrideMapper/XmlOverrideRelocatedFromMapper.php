<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideRelocatedFromMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\RelocatedFrom $element, CodeBook\RelocatedFrom $entity): CodeBook\RelocatedFrom
    {
        $entity->setIndent($element->isIndent());
        $entity->setAlign($element->getAlign());

        $entity->setReferenceId($element->getReferenceId());

        return $entity;
    }
}
