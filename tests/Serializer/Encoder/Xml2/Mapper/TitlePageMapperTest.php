<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CoverImage;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use App\Serializer\Encoder\Xml2\Mapper\TitlePageMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TitlePageMapper
 */
class TitlePageMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(TitlePage::class, $service->classMap);
        $this->assertIsCallable($service->classMap[TitlePage::class]);
        $this->assertInstanceOf(TitlePageMapper::class, $service->classMap[TitlePage::class][0]);

        $this->assertArrayHasKey(TitlePageMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TitlePageMapper::elementName()]);
        $this->assertInstanceOf(TitlePageMapper::class, $service->elementMap[TitlePageMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(TitlePage $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<titlepage %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new TitlePage();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(TitlePage $element, string $innerXml): void
    {
        $xml = sprintf('<titlepage xmlns="%s" xmlns:m="%s">%s</titlepage>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new TitlePage();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setCoverImage(new CoverImage());
        $innerXml .= '<cover-image/>';
        yield 'cover-image' => [deep_copy($element), $innerXml];
    }
}
