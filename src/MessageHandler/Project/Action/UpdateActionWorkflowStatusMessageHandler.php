<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\Project\Action;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\Project\CodeChange\AppendixCodeChange;
use App\Entity\Project\CodeChange\ChapterCodeChange;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Enum\ProposalActionStatus;
use App\Message\Project\Action\UpdateActionWorkflowStatusMessage;
use App\Traits\NumberCleanerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class UpdateActionWorkflowStatusMessageHandler
{
    use NumberCleanerTrait;

    private EntityRepository $nodeRepo;
    private EntityRepository $actionRepo;
    private bool $refreshParent = false;

    public function __construct(EntityManagerInterface $em)
    {
        $this->nodeRepo = $em->getRepository(AbstractCodeBookNode::class);
        $this->actionRepo = $em->getRepository(ProjectCodeChange::class);
    }

    public function __invoke(UpdateActionWorkflowStatusMessage $message): void
    {
        $action = $message->getAction();
        $this->refreshParent = $message->getRefreshParent();
        $this->updateWorkflowStatus($action);
    }

    public function updateWorkflowStatus(ProjectCodeChange $action): void
    {
        if ($action->getStatus() === ProposalActionStatus::INCORPORATED) {
            return;
        }

        $this->findParent($action);
        $this->findCodeBookNode($action);
        $this->updateStatus($action);
    }

    private function findParent(ProjectCodeChange $action): void
    {
        if (($action->getParentNode() && $action->getParentSection() && $action->getParentChapter()) && !$this->refreshParent) {
            return;
        }

        if (!empty($action->getParentPathId())) {

            if ($action instanceof ChapterCodeChange) {
                $parentNodes = [$action->getProject()->getCodeBook()->getVolume()];
            } else if ($action instanceof AppendixCodeChange) {
                $parentNodes = [$action->getProject()->getCodeBook()->getVolume()->getBackMatter()];
            } else {

                $parentNodes = $this->nodeRepo->findBy([
                    'publication' => $action->getProject()->getCodeBook(),
                    'pathId'      => $action->getParentPathId(),
                    'deletedDate' => null,
                ]);
            }

            if (count($parentNodes) === 1) {
                $action->setParentNode($parentNodes[0]);
                $action->setParentSection($parentNodes[0]->getNodeId());
                $chapterParent = $parentNodes[0]->getChapterParent();

                if ($chapterParent instanceof Chapter || $chapterParent instanceof Appendix) {
                    $chapterNodeId = $chapterParent->getNodeId();
                    $chapterNumber = $this->getCleanOrdinal($chapterParent->getNumber());

                    $action->setParentChapter($chapterNodeId);
                    $action->setChapterNumber($chapterNumber);
                }
            }
        }
    }

    private function findCodeBookNode(ProjectCodeChange $action): void
    {
        if ($action->getCodeBookNode()) {
            return;
        }

        $nodes = $this->nodeRepo->findBy([
            'publication' => $action->getProject()->getCodeBook(),
            'pathId'      => $action->getPathId(),
            'deletedDate' => null,
        ]);

        if (count($nodes) === 1) {
            $action->setCodeBookNode($nodes[0]);
        }
    }

    /**
     * @note references https://icc-it.atlassian.net/wiki/spaces/CT/pages/7431127041/Proposal+Action+Workflow+Matrix
     */
    private function updateStatus(ProjectCodeChange $action): void
    {
        // (1) has no match
        if (empty($action->getPathId())) {
            $action->setStatus(ProposalActionStatus::NEEDS_ACTION);
            return;
        }

        // (2) no parent, no match.
        if (empty($action->getParentNode()) && empty($action->getCodeBookNode())) {
            $action->setStatus(ProposalActionStatus::PARENT_DOES_NOT_EXIST);
            return;
        }

        // (9) already incorporated
        if ($action->isCodesIncorporated()) {
            $action->setStatus(ProposalActionStatus::INCORPORATED);
            return;
        }

        $isNew = empty($action->getCdpCodeChange()->getOriginalXmlId());
        $duplicateActions = $this->actionRepo->findBy([
            'project' => $action->getProject(),
            'pathId'  => $action->getPathId(),
            'deletedDate' => null,
        ]);

        if ($action->getId() == 723 ) {
            dd($duplicateActions);
        }
        $firstDupe = $duplicateActions[0] ?? null;

        // (3) and (4) no parent, but matches code section
        // this happens when a code section is renumbered to a new, non-existing, number.
        if (!$isNew && empty($action->getParentNode()) && $action->getCodeBookNode() instanceof AbstractCodeBookNode) {
            if ($action === $firstDupe) {
                // (3)
                $action->setStatus(ProposalActionStatus::PARENT_DOES_NOT_EXIST);
            } else {
                // (4)
                $action->setStatus(ProposalActionStatus::PARENT_DOES_NOT_EXIST_DUPLICATE);
            }
            return;
        }

        // (5) and (6) has parent, but is new code section
        // this happens when an action is adding a new code section
        if ($isNew && $action->getParentNode() instanceof AbstractCodeBookNode && empty($action->getCodeBookNode())) {
            if ($action === $firstDupe) {
                // (5)
                $action->setStatus(ProposalActionStatus::READY_TO_IMPORT);
            } else {
                // (6)
                $action->setStatus(ProposalActionStatus::DUPLICATE_DETECTED);
            }
            return;
        }

        // (7) and (8) both parent and code section are valid.
        if ($action->getParentNode() && $action->getCodeBookNode()) {
            if ($action === $firstDupe) {
                // (7)
                $action->setStatus(ProposalActionStatus::READY_TO_INCORPORATE);
            } else {
                // (8)
                $action->setStatus(ProposalActionStatus::DUPLICATE_DETECTED);
            }
            return;
        }

        // Fallback status
        $action->setStatus(ProposalActionStatus::NEEDS_ACTION);
    }
}
