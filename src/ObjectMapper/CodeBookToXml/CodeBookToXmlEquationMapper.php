<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook\Equation;
use App\Serializer\Encoder\Xml2\Element\Equation as XmlEquation;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;

class CodeBookToXmlEquationMapper extends AbstractCodeBookToXmlMapper
{
    public function map(Equation $from): XmlEquation
    {
        $equation = new XmlEquation();

        $this->mapCommonAttributes($from, $equation);
        $this->mapRevisionAttributes($from, $equation);

        if ($from->getLabel()) {
            $label = new Label();
            $label->setBody($from->getLabel());
            $equation->setLabel($label);
        }

        if ($from->getNumber()) {
            $number = new Number();
            $number->setBody($from->getNumber());
            $equation->setNumber($number);
        }

        $equation->setMath($from->getMath());
        $equation->setWhereList($from->getWhereList());

        return $equation;
    }
}
