<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookFigureMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\Figure $from): CodeBook\Figure
    {
        $to = new CodeBook\Figure();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setTocEntry($from->hasTocEntry());
        $to->setFloat($from->getFloat());
        $to->setOrientation($from->getOrientation());

        $to->setMedia($from->getMedia());
        $to->setCaption($from->getCaption());
        $to->setFigureNotesTitle($from->getFigureNotesTitle());
        $to->setFigureNotes($from->getFigureNotes());
        $to->setLegend($from->getLegend());
        $to->setSource($from->getSource());
        $to->setCreditTitle($from->getCreditTitle());
        $to->setCredit($from->getCredit());

        return $to;
    }
}
