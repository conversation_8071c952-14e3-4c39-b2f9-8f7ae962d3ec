<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlVolumeMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Volume $from): Xml2\Volume
    {
        $to = new Xml2\Volume();
        $this->mapCommonAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        $metadata = new Xml2\Metadata();
        $to->setMetadata($metadata);

        $to->setCustomerId($from->getCustomerId());
        $to->setTitleType($from->getTitleType());
        $to->setGoverningType($from->getGoverningType());

        if (!empty($from->getMetaTitle())) {
            $metadata->addMeta('title', $from->getMetaTitle());
        }

        if (!empty($from->getEdition())) {
            $metadata->addMeta('edition', $from->getEdition());
        }

        if (!empty($from->getParentDocument())) {
            $metadata->addMeta('parent-document', $from->getParentDocument());
        }

        if (!empty($from->getPublicationId())) {
            $metadata->addMeta('publication-id', $from->getPublicationId());
        }

        if (!empty($from->getYear())) {
            $metadata->addMeta('year', $from->getYear());
        }

        if (!empty($from->getPublicationAbbreviation())) {
            $metadata->addMeta('publication-abbrev', $from->getPublicationAbbreviation());
        }

        if (!empty($from->getVersion())) {
            $metadata->addMeta('version', $from->getVersion());
        }

        if (!empty($from->getOrigin())) {
            $metadata->addMeta('origin', $from->getOrigin());
        }

        if (!empty($from->getDateOrigin())) {
            $metadata->addMeta('date-origin', $from->getDateOrigin());
        }

        if (!empty($from->getModifiedBy())) {
            $metadata->addMeta('modified-by', $from->getModifiedBy());
        }

        if (!empty($from->getDateUpdated())) {
            $metadata->addMeta('date-updated', $from->getDateUpdated());
        }

        $children = [];
        foreach ($from->getChildren() as $child) {
            $children[] = $this->mapper->map($child);
        }
        $to->setChildren($children);

        return $to;
    }
}
