<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookForewordMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\Foreword $from): CodeBook\Foreword
    {
        $to = new CodeBook\Foreword();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setTocEntry($from->hasTocEntry());
        $to->setBody($from->getBody());

        if ($this->mapper) {
            $children = [];
            foreach ($from->getChildren() as $child) {
                $children[] = $this->mapper->map($child);
            }
            $to->setChildren($children);
        }

        return $to;
    }
}
