<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Mapper\IndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\IndexEntryMapper
 */
class IndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(IndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[IndexEntry::class]);
        $this->assertInstanceOf(IndexEntryMapper::class, $service->classMap[IndexEntry::class][0]);

        $this->assertArrayHasKey(IndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[IndexEntryMapper::elementName()]);
        $this->assertInstanceOf(IndexEntryMapper::class, $service->elementMap[IndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(IndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<index-entry %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new IndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

//        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(IndexEntry $element, string $innerXml): void
    {
        $xml = sprintf('<index-entry xmlns="%s" xmlns:m="%s">%s</index-entry>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new IndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $primaryIE = new PrimaryIndexEntry();
        $primaryIE->setId('id');
        $element->setPrimaryIndexEntry($primaryIE);
        $innerXml .= '<primaryie id="id"/>';
        yield 'primaryie' => [deep_copy($element), $innerXml];
    }
}
