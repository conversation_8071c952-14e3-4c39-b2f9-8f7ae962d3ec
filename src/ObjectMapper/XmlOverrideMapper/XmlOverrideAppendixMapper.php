<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverrideAppendixMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Appendix $element, CodeBook\Appendix $entity): CodeBook\Appendix
    {
        $this->mapTitleGroup($element, $entity);

        if ($history = $element->getHistory()) {
            $entity->setHistory($history->getBody());
        }

        if ($objectives = $element->getObjectives()) {
            $entity->setObjectives($objectives->getBody() ? $objectives->getBody()->getBody() : '');

            if ($objectives->getTitleGroup() && $title = $objectives->getTitleGroup()->getTitle()) {
                $entity->setObjectivesTitle($title->getBody());
            }
        }

        if ($note = $element->getNote()) {
            $entity->setNote($note->getBody());

            if ($note->getTitleGroup() && $title = $note->getTitleGroup()->getTitle()) {
                $entity->setNoteTitle($title->getBody());
            }
        }

        if ($abstract = $element->getAbstract()) {
            $entity->setAbstract($abstract->getBody());

            if ($abstract->getTitleGroup() && $title = $abstract->getTitleGroup()->getTitle()) {
                $entity->setAbstractTitle($title->getBody());
            }
        }

        if ($keywords = $element->getKeywords()) {
            $entity->setKeywords($keywords->getBody());

            if ($keywords->getTitleGroup() && $title = $keywords->getTitleGroup()->getTitle()) {
                $entity->setKeywordsTitle($title->getBody());
            }
        }

        if ($body = $element->getBody()) {
            $entity->setBody($body->getBody());
        }

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
