<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\EventSubscriber;

use App\Helper\Xml2Helper;
use App\Serializer\Encoder\Xml2\Event\PostEncodeEvent;
use App\Service\Xml2\ChangeMarkerAttributeSanitizer;
use DOMElement;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

use function sprintf;

//#[AsEventListener(event: PostEncodeEvent::class, method: 'onPostEncode')]
class ChangeMarkerValidationSubscriber
{

    public function __construct(
        private readonly ChangeMarkerAttributeSanitizer $sanitizer,
        private readonly LoggerInterface $logger
    ) {}

    public function onPostEncode(PostEncodeEvent $event): void
    {
        $xml = $event->getXml();
        if (empty($xml)) {
            return;
        }

        $document = Xml2Helper::createDOMDocument($xml);
        $updates = $this->sanitizer->sanitize($document, $event->getProject());
        if ([] !== $updates) {
            $event->setXml($document->saveXML($document->documentElement));
        }

        $xpath = Xml2Helper::createDOMXPath($document);

        $query = '//x:insert[not(@data-changed) or normalize-space(@data-changed) = "" or not(@revision) or normalize-space(@revision) = ""]'
            . ' | //x:delete[not(@data-changed) or normalize-space(@data-changed) = "" or not(@revision) or normalize-space(@revision) = ""]';

        $nodes = $xpath->query($query, $document->documentElement);
        if (false === $nodes || 0 === $nodes->length) {
            return;
        }

        $first = $nodes->item(0);
        $tag = $first instanceof DOMElement ? $first->tagName : 'node';

        $this->logger->error(sprintf('Exported XML contains <%s> elements missing required change tracking attributes.', $tag));
    }
}
