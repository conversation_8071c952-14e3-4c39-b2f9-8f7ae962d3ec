<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class SeeAlsoIndexEntry extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\ReferenceId;
    public string $prefix = 'See also';

    public ?Term $term = null;
    public string $navPointerGroup = '';

    public function getPrefix(): string
    {
        return $this->prefix;
    }

    public function setPrefix(string $prefix): void
    {
        $this->prefix = $prefix;
    }

    public function getTerm(): ?Term
    {
        return $this->term;
    }

    public function setTerm(?Term $term): void
    {
        $this->term = $term;
    }

    public function getNavPointerGroup(): string
    {
        return $this->navPointerGroup;
    }

    public function setNavPointerGroup(string $navPointerGroup): void
    {
        $this->navPointerGroup = $navPointerGroup;
    }
}
