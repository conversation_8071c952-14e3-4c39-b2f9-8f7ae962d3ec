<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\DefinitionList;
use App\Serializer\Dto\Book\DefinitionListDto;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

use function json_decode;
use function json_encode;

class DefinitionListToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            DefinitionList::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof DefinitionList
            && 'json' === $format;
    }

    /** @param DefinitionList $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new DefinitionListDto();
        $this->mapCommon($object, $dto);
//        $this->mapRevisionAttributes($object, $dto);
//        $this->mapQrCode($object, $dto);
//        $this->mapTitleGroup($object, $dto);

       try {
           $skip = $context['bookContents'] ?? false;
           if (!$skip) {
               $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
               $dto->setChildren($children);
           }
       } catch (ExceptionInterface $exception) {
       }

        return json_decode(json_encode($dto), true);
    }
}
