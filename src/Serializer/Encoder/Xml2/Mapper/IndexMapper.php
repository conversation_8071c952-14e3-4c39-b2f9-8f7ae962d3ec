<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;

/**
 * @since 1.11
 */
class IndexMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'index';
    }

    public static function dtoClass(): string
    {
        return Index::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Index);

        if (array_key_exists('tocentry', $attrs)) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Index);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('indexdiv' === $key && $value instanceof IndexDivision) {
                $children[] = $value;
            } elseif ('index-entry' === $key && $value instanceof IndexEntry) {
                $children[] = $value;
            } elseif ('nonxml' === $key) {
                throw new \RuntimeException('NonXml not implemented');
            }
        }
        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Index);

        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Index);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof IndexDivision) {
                $writer->writeElement(self::CLARK . 'indexdiv', $i);
            } elseif ($i instanceof IndexEntry) {
                $writer->writeElement(self::CLARK . 'index-entry', $i);
            }
        }
    }
}
