<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Enum\Rules;

/**
 * @since 1.11
 */
trait HtmlTableAttributes
{
    public string $class = '';
    public string $titleAttr = '';
    public string $summary = '';
    public string $width = '';
    public string $border = '';
    public int $cellSpacing = 0;
    public int $cellPadding = 0;
    public string $rules = Rules::NONE;

    public function getClass(): string
    {
        return $this->class;
    }

    public function setClass(string $class): void
    {
        $this->class = $class;
    }

    public function getTitleAttr(): string
    {
        return $this->titleAttr;
    }

    public function setTitleAttr(string $titleAttr): void
    {
        $this->titleAttr = $titleAttr;
    }

    public function getSummary(): string
    {
        return $this->summary;
    }

    public function setSummary(string $summary): void
    {
        $this->summary = $summary;
    }

    public function getWidth(): string
    {
        return $this->width;
    }

    public function setWidth(string $width): void
    {
        $this->width = $width;
    }

    public function getBorder(): string
    {
        return $this->border;
    }

    public function setBorder(string $border): void
    {
        $this->border = $border;
    }

    public function getCellSpacing(): int
    {
        return $this->cellSpacing;
    }

    public function setCellSpacing(int $cellSpacing): void
    {
        $this->cellSpacing = $cellSpacing;
    }

    public function getCellPadding(): int
    {
        return $this->cellPadding;
    }

    public function setCellPadding(int $cellPadding): void
    {
        $this->cellPadding = $cellPadding;
    }

    public function getRules(): string
    {
        return $this->rules;
    }

    public function setRules(string $rules): void
    {
        $this->rules = $rules;
    }
}
