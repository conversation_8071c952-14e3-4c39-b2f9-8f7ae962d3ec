<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToPrefaceMapper;
use App\ObjectMapper\XmlToBaseBook\XmlToReferenceStandardMapper;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToPrefaceMapper */
class XmlToReferenceStandardMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToReferenceStandardMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToReferenceStandardMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Reference $element, BaseBook\ReferenceStandard $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Reference();
        $expected = new BaseBook\ReferenceStandard();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);

        $number = new Number();
        $number->setBody('Number');
        $titleGroup = new TitleGroup();
        $titleGroup->setNumber($number);

        $element->setTitleGroup($titleGroup);
        $expected->setNumber('Number');
        yield 'number' => [deep_copy($element), deep_copy($expected)];

        $title = new Title();
        $title->setBody('Title');

        $titleGroup->setTitle($title);
        $expected->setTitle('Title');
        yield 'title' => [deep_copy($element), deep_copy($expected)];

        $element->setNavPointerGroup('<nav-pointer-group/>');
        $expected->setNavPointerGroup('<nav-pointer-group/>');
        yield 'nav-pointer-group' => [deep_copy($element), deep_copy($expected)];
    }
}
