<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\CommonBook;

/**
 * @since 1.13
 */
trait FigureFields
{
    use Attribute\RevisionAttributes;
    use Attribute\TocEntry;
    use Attribute\FloatAttribute;
    use Attribute\Orientation;

    use Field\TitleGroup;
    use Field\Media;
    use Field\Caption;
    use Field\FigureNotes;
    use Field\Legend;
    use Field\Source;
    use Field\Credit;
}
