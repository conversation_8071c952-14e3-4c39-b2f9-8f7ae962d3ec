<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class DefinitionItem extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\IndexNumber;

    public ?CommitteeDesignation $committeeDesignation = null;
    public ?Term $term = null;
    public ?Definition $definition = null;

    public function getCommitteeDesignation(): ?CommitteeDesignation
    {
        return $this->committeeDesignation;
    }

    public function setCommitteeDesignation(?CommitteeDesignation $committeeDesignation): void
    {
        $this->committeeDesignation = $committeeDesignation;
    }

    public function getTerm(): ?Term
    {
        return $this->term;
    }

    public function setTerm(Term $term): void
    {
        $this->term = $term;
    }

    public function getDefinition(): ?Definition
    {
        return $this->definition;
    }

    public function setDefinition(?Definition $definition): void
    {
        $this->definition = $definition;
    }
}
