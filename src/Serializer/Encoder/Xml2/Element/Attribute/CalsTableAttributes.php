<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Enum\Frame;

/**
 * @since 1.11
 */
trait CalsTableAttributes
{
    use Orientation;
    use FloatAttr;
    use TocEntry;
    use PageWide;

    public string $frame = Frame::NONE;
    public bool $columnSeparator = false;
    public bool $rowSeparator = false;
    public string $backgroundColor = '';
    public string $tableStyle = '';

    public function getFrame(): string
    {
        return $this->frame;
    }

    public function setFrame(string $frame): void
    {
        $this->frame = $frame;
    }

    public function isColumnSeparator(): bool
    {
        return $this->columnSeparator;
    }

    public function setColumnSeparator(bool $columnSeparator): void
    {
        $this->columnSeparator = $columnSeparator;
    }

    public function isRowSeparator(): bool
    {
        return $this->rowSeparator;
    }

    public function setRowSeparator(bool $rowSeparator): void
    {
        $this->rowSeparator = $rowSeparator;
    }

    public function getBackgroundColor(): string
    {
        return $this->backgroundColor;
    }

    public function setBackgroundColor(string $backgroundColor): void
    {
        $this->backgroundColor = $backgroundColor;
    }

    public function getTableStyle(): string
    {
        return $this->tableStyle;
    }

    public function setTableStyle(string $tableStyle): void
    {
        $this->tableStyle = $tableStyle;
    }
}
