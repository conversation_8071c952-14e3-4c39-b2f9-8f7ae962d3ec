<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

/**
 * @since 1.11
 */
trait IndexNumber
{
    public string $indexNumber = '';

    public function getIndexNumber(): string
    {
        return $this->indexNumber;
    }

    public function setIndexNumber(string $indexNumber): void
    {
        $this->indexNumber = $indexNumber;
    }
}
