<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlChapterMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Chapter $from): Xml2\Level
    {
        $to = new Xml2\Level();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setIndexNumber($from->getIndexNumber());
        $to->setReserveCount($from->getReserveCount());
        $to->setTocEntry($from->hasTocEntry());
        $to->setTocAutoAdd($from->isTocAutoAdd());
        $to->setDisplayLevel($from->getDisplayLevel());

        if (!empty($from->getRelocatedFrom())) {
            $relocatedFrom = new Xml2\RelocatedFrom();
            $relocatedFrom->setBody($from->getRelocatedFrom());
            $relocatedFrom->setReferenceId($from->getRelocatedFromId());
            $to->setRelocatedFrom($relocatedFrom);
        }
        if (!empty($from->getHistory())) {
            $history = new Xml2\History();
            $history->setBody($from->getHistory());
            $to->setHistory($history);
        }
        $to->setObjectives($this->createObjectives($from->getObjectivesTitle(), $from->getObjectives()));
        $to->setAbstract($this->createAbstract($from->getAbstractTitle(), $from->getAbstract()));
        $to->setKeywords($this->createKeywords($from->getKeywordsTitle(), $from->getKeywords()));
        if (!empty($from->getBody())) {
            $body = new Xml2\SectionBody();
            $body->setBody($from->getBody());
            $to->setBody($body);
        }

        $children = [];
        foreach ($from->getChildren() as $child) {
            $children[] = $this->mapper->map($child);
        }
        $to->setChildren($children);

        return $to;
    }
}
