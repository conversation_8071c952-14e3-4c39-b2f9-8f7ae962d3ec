<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookTitlePageMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\TitlePage $from): CodeBook\TitlePage
    {
        $to = new CodeBook\TitlePage();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setBody($from->getBody());

        return $to;
    }
}
