<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Publication;
use App\Serializer\Encoder\Xml2\Element\Volume;
use App\Serializer\Encoder\Xml2\Xml2Schema;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function time;

/**
 * @since 1.16
 */
class PublicationMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'publication';
    }

    public static function dtoClass(): string
    {
        return Publication::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Publication);

        $this->readDecimalAttributes($element, $attrs, [
            'schemaVersion'     => 'schema-version',
            'schematronVersion' => 'schematron-version',
            'documentVersion'   => 'document-version',
        ]);
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Publication);

        $volumes = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('metadata' === $key && $value instanceof Metadata) {
                $element->setMetadata($value);
            } elseif ('volume' === $key && $value instanceof Volume) {
                $volumes[] = $value;
            }
        }

        $element->setChildren($volumes);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Publication);

        $attrs['schema-version'] = (string) ($element->getSchemaVersion() > -0
            ? $element->getSchemaVersion()
            : Xml2Schema::VERSION);

        $this->writeDecimalAttributes($element, $attrs, [
            'schematronVersion' => 'schematron-version',
            'documentVersion'   => 'document-version',
        ]);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Publication);

        if ($element->getMetadata()) {
            $writer->writeElement(self::CLARK . 'metadata', $element->getMetadata());
        }

        foreach ($element->getChildren() as $i) {
            $writer->writeElement(self::CLARK . 'volume', $i);
        }
    }

    protected function getTimestamp(): string
    {
        return (string) time();
    }
}
