<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlBackMatterMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlBackMatterMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlBackMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlBackMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\BackMatter $from, Xml2\BackMatter $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\BackMatter();
        $from->setNodeId('');
        $expected = new Xml2\BackMatter();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);

        $childChapter = new CodeBook\Appendix();
        $childChapter->setNodeId('childNode');
        $from->addChild($childChapter);

        $expectedChild = new Xml2\Appendix();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
