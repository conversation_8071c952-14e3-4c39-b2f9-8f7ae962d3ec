<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideCopyrightPageMapper;
use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideCopyrightPageMapper */
class XmlOverrideCopyrightPageMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideCopyrightPageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideCopyrightPageMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(CopyrightPage $element, CodeBook\CopyrightPage $expected): void
    {
        $entity = new CodeBook\CopyrightPage();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new CopyrightPage();
        $expected = new CodeBook\CopyrightPage();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        yield from $this->titleGroupCases($element, $expected);

        // Body
        $element->setBody('<p>Copyright body</p>');
        $expected->setBody('<p>Copyright body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];
    }
}
