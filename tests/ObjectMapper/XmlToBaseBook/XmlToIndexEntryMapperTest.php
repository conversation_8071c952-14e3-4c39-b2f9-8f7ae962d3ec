<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Element\TertiaryIndexEntry;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToIndexEntryMapper */
class XmlToIndexEntryMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(PrimaryIndexEntry $element, BaseBook\IndexEntry $expected, array $children = []): void
    {
        $entry = new IndexEntry();
        $entry->setPrimaryIndexEntry($element);
        $entry->setChildren($children);

        $actual = $this->mapper->map($entry);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new PrimaryIndexEntry();
        $expected = new BaseBook\IndexEntry();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);

        $term = new Term();
        $term->setBody('Term');
        $element->setTerm($term);
        $expected->setTerm('Term');
        yield 'term' => [deep_copy($element), deep_copy($expected)];

        $navPointerGroup = <<<XML
<nav-pointer-group>
    <nav-pointer/>
</nav-pointer-group>
XML;
        $element->setNavPointerGroup($navPointerGroup);
        $expected->setNavPointerGroup($navPointerGroup);
        yield 'nav-pointer-group' => [deep_copy($element), deep_copy($expected)];

        $children = [new SecondaryIndexEntry(), new TertiaryIndexEntry()];
        $expected->setChildren([new BaseBook\SecondaryIndexEntry(), new BaseBook\TertiaryIndexEntry()]);
        yield 'children' => [deep_copy($element), deep_copy($expected), $children];
    }
}
