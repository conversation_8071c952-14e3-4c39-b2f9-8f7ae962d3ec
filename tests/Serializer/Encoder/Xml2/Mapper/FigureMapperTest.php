<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\FigureNotes;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\FigureMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\FigureMapper
 */
class FigureMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Figure::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Figure::class]);
        $this->assertInstanceOf(FigureMapper::class, $service->classMap[Figure::class][0]);

        $this->assertArrayHasKey(FigureMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[FigureMapper::elementName()]);
        $this->assertInstanceOf(FigureMapper::class, $service->elementMap[FigureMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Figure $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<figure %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Figure();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];

        $element->setFloat(FloatEnum::TOP);
        $attrs['float'] = FloatEnum::TOP;
        yield 'float' => [deep_copy($element), $attrs];

        $element->setOrientation(Orientation::LANDSCAPE);
        $attrs['orient'] = Orientation::LANDSCAPE;
        yield 'orient' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Figure $element, string $innerXml): void
    {
        $xml = sprintf('<figure xmlns="%s" xmlns:m="%s">%s</figure>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Figure();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setMedia('<mediaobject src="" alt=""/>');
        $innerXml .= '<mediaobject src="" alt=""/>';
        yield 'media' => [deep_copy($element), $innerXml];

        $element->setCaption(new Caption());
        $innerXml .= '<caption/>';
        yield 'caption' => [deep_copy($element), $innerXml];

        $element->setFigureNotes(new FigureNotes());
        $innerXml .= '<figure-notes/>';
        yield 'figure-notes' => [deep_copy($element), $innerXml];

        $element->setLegend('<legend>Legend</legend>');
        $innerXml .= '<legend>Legend</legend>';
        yield 'legend' => [deep_copy($element), $innerXml];

        $element->setSource(new Source());
        $innerXml .= '<source/>';
        yield 'source' => [deep_copy($element), $innerXml];

        $element->setCredit(new Credit());
        $innerXml .= '<credit/>';
        yield 'credit' => [deep_copy($element), $innerXml];
    }
}
