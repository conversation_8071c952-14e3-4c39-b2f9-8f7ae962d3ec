<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Email;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class EmailMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'email';
    }

    public static function dtoClass(): string
    {
        return Email::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Email);

        if (array_key_exists('mailto', $attrs) && is_string($attrs['mailto'])) {
            $element->setMailTo($attrs['mailto']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Email);

        if (!empty($element->getMailTo())) {
            $attrs['mailto'] = $element->getMailTo();
        }
    }
}
