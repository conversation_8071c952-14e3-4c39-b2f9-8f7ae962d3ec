<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexDivisionMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlIndexDivisionMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlIndexDivisionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexDivisionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\IndexDivision $from, Xml2\IndexDivision $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\IndexDivision();
        $from->setNodeId('');
        $expected = new Xml2\IndexDivision();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $childIndex = new CodeBook\Index();
        $childIndex->setNodeId('childNode');
        $from->addChild($childIndex);

        $expectedChild = new Xml2\Index();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
