<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\EventSubscriber\Project\Export;

use App\Event\Project\PostExportProject;
use App\Helper\Xml2Helper;
use App\Service\Xml2\ChangeMarkerAttributeSanitizer;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

use function mb_substr;
use function mb_trim;
use function strlen;

#[AsEventListener(event: PostExportProject::class, method: 'validateChangeMarkers')]
class ValidateChangeMarkersSubscriber
{
    private const MAX_SNIPPET_LENGTH = 500;

    public function __construct(
        private readonly LoggerInterface $projectXmlLogger,
        private readonly ChangeMarkerAttributeSanitizer $sanitizer
    ) {
    }

    public function validateChangeMarkers(PostExportProject $event): void
    {
        $xml = $event->getXml();
        if ('' === mb_trim($xml)) {
            return;
        }

        $document = Xml2Helper::createDOMDocument($xml);
        $updates = $this->sanitizer->sanitize($document, $event->getProject());

        if ([] === $updates) {
            return;
        }

        $logUpdates = [];

        foreach ($updates as $update) {
            $node = $update['node'];
            $snippet = $document->saveXML($node) ?: '';
            $snippet = Xml2Helper::stripDeclaration($snippet);
            if (strlen($snippet) > self::MAX_SNIPPET_LENGTH) {
                $snippet = mb_substr($snippet, 0, self::MAX_SNIPPET_LENGTH) . '…';
            }

            $logUpdates[] = [
                'tag' => $update['tag'],
                'attributes_added' => $update['attributes_added'],
                'snippet' => $snippet,
            ];
        }

        $this->projectXmlLogger->warning('Sanitized change marker attributes during export.', [
            'book_id' => $event->getProject()->getShortCode(),
            'updates' => $logUpdates,
        ]);

        $event->setXml($document->saveXML($document->documentElement));
    }
}
