<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTableMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlTableMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlTableMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlTableMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Table $from, Xml2\Table $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Table();
        $from->setNodeId('');
        $expected = new Xml2\Table();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'orientation' => [deep_copy($from), deep_copy($expected)];

        $from->setFloat(FloatEnum::MARGIN);
        $expected->setFloat(FloatEnum::MARGIN);
        yield 'float' => [deep_copy($from), deep_copy($expected)];

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setPageWide(true);
        $expected->setPageWide(true);
        yield 'pageWide' => [deep_copy($from), deep_copy($expected)];

        $from->setFrame(Frame::TOP);
        $expected->setFrame(Frame::TOP);
        yield 'frame' => [deep_copy($from), deep_copy($expected)];

        $from->setColumnSeparator(true);
        $expected->setColumnSeparator(true);
        yield 'columnSeparator' => [deep_copy($from), deep_copy($expected)];

        $from->setRowSeparator(true);
        $expected->setRowSeparator(true);
        yield 'rowSeparator' => [deep_copy($from), deep_copy($expected)];

        $from->setBackgroundColor('backgroundColor');
        $expected->setBackgroundColor('backgroundColor');
        yield 'backgroundColor' => [deep_copy($from), deep_copy($expected)];

        $from->setTableStyle('tableStyle');
        $expected->setTableStyle('tableStyle');
        yield 'tableStyle' => [deep_copy($from), deep_copy($expected)];

        $from->setClass('class');
        $expected->setClass('class');
        yield 'class' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleAttr('titleAttr');
        $expected->setTitleAttr('titleAttr');
        yield 'titleAttr' => [deep_copy($from), deep_copy($expected)];

        $from->setSummary('summary');
        $expected->setSummary('summary');
        yield 'summary' => [deep_copy($from), deep_copy($expected)];

        $from->setWidth('width');
        $expected->setWidth('width');
        yield 'width' => [deep_copy($from), deep_copy($expected)];

        $from->setBorder('border');
        $expected->setBorder('border');
        yield 'border' => [deep_copy($from), deep_copy($expected)];

        $from->setCellPadding(100);
        $expected->setCellPadding(100);
        yield 'cellPadding' => [deep_copy($from), deep_copy($expected)];

        $from->setCellSpacing(100);
        $expected->setCellSpacing(100);
        yield 'cellSpacing' => [deep_copy($from), deep_copy($expected)];

        $from->setRules(Rules::COLS);
        $expected->setRules(Rules::COLS);
        yield 'rules' => [deep_copy($from), deep_copy($expected)];

        $from->setTable('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $from->setCaption('caption');
        $caption = new Xml2\Caption();
        $caption->setBody('caption');
        $expected->setCaption($caption);
        yield 'caption' => [deep_copy($from), deep_copy($expected)];

        $from->setLegend('legend');
        $expected->setLegend('legend');
        yield 'legend' => [deep_copy($from), deep_copy($expected)];

        $from->setTableNotes('tableNotes');
        $tableNotes = new Xml2\TableNotes();
        $tableNotes->setBody('tableNotes');
        $expected->setTableNotes($tableNotes);
        yield 'tableNotes' => [deep_copy($from), deep_copy($expected)];

        $from->setTableNotesTitle('tableNotesTitle');
        $title = new Xml2\Title();
        $title->setBody('tableNotesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $tableNotes->setTitleGroup($titleGroup);
        yield 'tableNotesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setSource('source');
        $source = new Xml2\Source();
        $source->setBody('source');
        $expected->setSource($source);
        yield 'source' => [deep_copy($from), deep_copy($expected)];

        $from->setCredit('credit');
        $credit = new Xml2\Credit();
        $credit->setBody('credit');
        $expected->setCredit($credit);
        yield 'credit' => [deep_copy($from), deep_copy($expected)];

        $from->setCreditTitle('creditTitle');
        $title = new Xml2\Title();
        $title->setBody('creditTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $credit->setTitleGroup($titleGroup);
        yield 'creditTitle' => [deep_copy($from), deep_copy($expected)];
    }
}
