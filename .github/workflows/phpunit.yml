name: phpunit

on:
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  CONTAINER_IMAGE: ${{ vars.ICC_CR_DOMAIN }}/${{ vars.CR_REPOSITORY }}

permissions:
  contents: read

jobs:
  phpunit:
    name: phpunit
    runs-on: self-hosted

    strategy:
      max-parallel: 2
      matrix:
        php-versions: [ '8.2', '8.3', '8.4' ]

    steps:
      - name: Reset workspace permissions
        run: |
          sudo chmod -R u+rwX $GITHUB_WORKSPACE
          sudo chown -R $(id -u):$(id -g) $GITHUB_WORKSPACE

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install auth.json
        run: echo '${{ secrets.COMPOSER_AUTH_JSON_HTTP_BASIC }}' > $GITHUB_WORKSPACE/auth.json

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-2
          role-to-assume: arn:aws:iam::286574893818:role/gh-actions-ecr-role

      - name: Login to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: /tmp/composer/
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Start containers
        run: docker compose -f compose.phpunit.yaml up -d --force-recreate --wait --wait-timeout 60 mariadb phpunit
        env:
          IMAGE: ${{ env.CONTAINER_IMAGE }}:phpunit-${{ matrix.php-versions }}
          PHP_VERSION: ${{ matrix.php-versions }}
          COMPOSER_CACHE_DIR: /tmp/composer/

      - name: Install dependencies
        run: docker compose -f compose.phpunit.yaml exec phpunit composer install --no-interaction --prefer-dist
        env:
          COMPOSER_CACHE_DIR: /tmp/composer/

      - name: Run phpunit
        run: docker compose -f compose.phpunit.yaml exec phpunit php vendor/bin/phpunit
        env:
          COMPOSER_CACHE_DIR: /tmp/composer/

      - name: Stop containers
        run: docker compose -f compose.phpunit.yaml down phpunit mariadb
