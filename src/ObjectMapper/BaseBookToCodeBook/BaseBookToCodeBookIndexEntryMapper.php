<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookIndexEntryMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\IndexEntry $from): CodeBook\IndexEntry
    {
        $to = new CodeBook\IndexEntry();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setReferenceId($from->getReferenceId());
        $to->setTerm($from->getTerm());
        $to->setNavPointerGroup($from->getNavPointerGroup());

        if ($this->mapper) {
            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }
        }

        return $to;
    }
}
