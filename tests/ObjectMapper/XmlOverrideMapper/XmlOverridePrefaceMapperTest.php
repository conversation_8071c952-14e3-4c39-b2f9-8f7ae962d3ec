<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverridePrefaceMapper;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverridePrefaceMapper */
class XmlOverridePrefaceMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverridePrefaceMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverridePrefaceMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Preface $element, CodeBook\Preface $expected): void
    {
        $entity = new CodeBook\Preface();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Preface();
        $expected = new CodeBook\Preface();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        //Title group
        yield from $this->titleGroupCases($element, $expected);

        // TocEntry = true
        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($element), deep_copy($expected)];

        // Body
        $element->setBody('<p>Preface content</p>');
        $expected->setBody('<p>Preface content</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        // TocEntry + Body together
        $element->setTocEntry(true);
        $element->setBody('<p>Full Preface</p>');
        $expected->setTocEntry(true);
        $expected->setBody('<p>Full Preface</p>');
        yield 'tocEntry+body' => [deep_copy($element), deep_copy($expected)];
    }
}
