<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Dto\Book\TableOfContentsDto;
use Sabre\Xml\Reader;
use <PERSON>bre\Xml\Writer;

/**
 * @since 1.11
 */
class TableOfContentsMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'toc';
    }

    public static function dtoClass(): string
    {
        return TableOfContentsDto::class;
    }

    /**
     * @param TableOfContentsDto $element
     */
    protected function readElement(Reader $reader, $element): void
    {
        $reader->next();
    }

    /**
     * @param TableOfContentsDto $element
     */
    protected function writeElement(Writer $writer, $element): void
    {
    }
}
