<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

use function sprintf;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251103220014 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE base_book_nodes DROP FOREIGN KEY FK_8045C3893DA5256D');
        $this->addSql('ALTER TABLE base_book_nodes DROP FOREIGN KEY FK_8045C389EE29562C');
        $this->addSql('ALTER TABLE code_book_figure DROP FOREIGN KEY FK_4FC15147EE29562C');
        $this->addSql('ALTER TABLE code_book_figure DROP FOREIGN KEY FK_4FC151473DA5256D');
        $this->addSql('ALTER TABLE media_object DROP FOREIGN KEY FK_14D43132D740CE0B');
        $this->addSql('ALTER TABLE media_object_group_media DROP FOREIGN KEY FK_D29D389C5EFF404B');
        $this->addSql('ALTER TABLE media_object_group_media DROP FOREIGN KEY FK_D29D389C64DE5A5');
        $this->addSql('DROP TABLE media_object');
        $this->addSql('DROP TABLE media_object_group_media');
        $this->addSql('DROP TABLE media_object_group');
        $this->addSql('DROP INDEX IDX_8045C3893DA5256D ON base_book_nodes');
        $this->addSql('DROP INDEX IDX_8045C389EE29562C ON base_book_nodes');
        $this->addSql('ALTER TABLE base_book_nodes DROP image_id, DROP deleted_image_id, CHANGE reference_id reference_id TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE code_book_audit_log CHANGE changes changes JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('DROP INDEX IDX_4FC15147EE29562C ON code_book_figure');
        $this->addSql('DROP INDEX IDX_4FC151473DA5256D ON code_book_figure');
        $this->addSql('ALTER TABLE code_book_figure DROP image_id, DROP deleted_image_id');
        $this->addSql('ALTER TABLE code_book_link_url DROP FOREIGN KEY FK_9CE163E92E05F71D');
        $this->addSql('DROP INDEX IDX_9CE163E92E05F71D ON code_book_link_url');
        $this->addSql('ALTER TABLE code_book_link_url CHANGE code_book_id publication_id INT NOT NULL');
        $this->addSql('ALTER TABLE code_book_link_url ADD CONSTRAINT FK_9CE163E938B217A7 FOREIGN KEY (publication_id) REFERENCES code_book_publication (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_9CE163E938B217A7 ON code_book_link_url (publication_id)');
        $this->addSql('ALTER TABLE code_book_link_xref DROP FOREIGN KEY FK_8C31F3CB2E05F71D');
        $this->addSql('ALTER TABLE code_book_link_xref ADD CONSTRAINT FK_8C31F3CB2E05F71D FOREIGN KEY (code_book_id) REFERENCES code_book_publication (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE code_book_reference_standard CHANGE number_year number_year LONGTEXT DEFAULT \'\' NOT NULL');
        $this->addSql('ALTER TABLE project CHANGE error_message error_message LONGTEXT DEFAULT NULL');

        foreach (['super_title', 'committee_designation', 'label', 'number', 'original_number', 'correlated', 'sub_title', 'title_history'] as $field) {
            $this->addSql(sprintf('UPDATE base_book_nodes SET `%s` = "" WHERE `%s` IS NULL', $field, $field));
        }
        $this->addSql('ALTER TABLE code_book_index ADD super_title LONGTEXT NOT NULL, ADD correlated LONGTEXT NOT NULL, ADD sub_title LONGTEXT NOT NULL, ADD title_history LONGTEXT NOT NULL, ADD committee_designation LONGTEXT NOT NULL, ADD label LONGTEXT NOT NULL, ADD number LONGTEXT NOT NULL, ADD original_number LONGTEXT NOT NULL');
        $this->addSql('ALTER TABLE code_book_index_division ADD super_title LONGTEXT NOT NULL, ADD correlated LONGTEXT NOT NULL, ADD sub_title LONGTEXT NOT NULL, ADD title_history LONGTEXT NOT NULL, ADD committee_designation LONGTEXT NOT NULL, ADD label LONGTEXT NOT NULL, ADD number LONGTEXT NOT NULL, ADD original_number LONGTEXT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE media_object (id INT AUTO_INCREMENT NOT NULL, remote_file_id INT DEFAULT NULL, alt VARCHAR(999) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, rendition VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, mime_type VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, revision_by VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, revision_date_time DATETIME DEFAULT NULL, revision VARCHAR(10) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, revision_group VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, data_changed VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, data_changed_in VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, relocated_from_attr VARCHAR(500) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, super_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, sub_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, committee_designation LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, label LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, number LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_abbreviation VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_year VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, long_description LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, caption LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, legend LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, disclaimer LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, source LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, credit LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, credit_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, dataType VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, align VARCHAR(10) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, valign VARCHAR(10) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, width VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, depth VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, content_width VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, content_depth VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, scale VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, page_wide TINYINT(1) DEFAULT 0 NOT NULL, scale_to_fit TINYINT(1) DEFAULT 0 NOT NULL, src VARCHAR(999) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, original_number LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, correlated LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_history LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, INDEX IDX_14D43132D740CE0B (remote_file_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE media_object_group_media (media_object_group_id INT NOT NULL, media_object_id INT NOT NULL, INDEX IDX_D29D389C5EFF404B (media_object_group_id), UNIQUE INDEX UNIQ_D29D389C64DE5A5 (media_object_id), PRIMARY KEY(media_object_group_id, media_object_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE media_object_group (id INT AUTO_INCREMENT NOT NULL, revision_by VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, revision_date_time DATETIME DEFAULT NULL, revision VARCHAR(10) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, revision_group VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, data_changed VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, data_changed_in VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, relocated_from_attr VARCHAR(500) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, super_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, sub_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, committee_designation LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, label LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, number LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_abbreviation VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_year VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, caption LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, disclaimer LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, source LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, credit LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, credit_title LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, original_number LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, correlated LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, title_history LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE media_object ADD CONSTRAINT FK_14D43132D740CE0B FOREIGN KEY (remote_file_id) REFERENCES remote_file (id)');
        $this->addSql('ALTER TABLE media_object_group_media ADD CONSTRAINT FK_D29D389C5EFF404B FOREIGN KEY (media_object_group_id) REFERENCES media_object_group (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE media_object_group_media ADD CONSTRAINT FK_D29D389C64DE5A5 FOREIGN KEY (media_object_id) REFERENCES media_object (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE code_book_index_division DROP super_title, DROP correlated, DROP sub_title, DROP title_history, DROP committee_designation, DROP label, DROP number, DROP original_number');
        $this->addSql('ALTER TABLE code_book_index DROP super_title, DROP correlated, DROP sub_title, DROP title_history, DROP committee_designation, DROP label, DROP number, DROP original_number');
        $this->addSql('ALTER TABLE base_book_nodes ADD image_id INT DEFAULT NULL, ADD deleted_image_id INT DEFAULT NULL, CHANGE reference_id reference_id LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE base_book_nodes ADD CONSTRAINT FK_8045C3893DA5256D FOREIGN KEY (image_id) REFERENCES media_object (id)');
        $this->addSql('ALTER TABLE base_book_nodes ADD CONSTRAINT FK_8045C389EE29562C FOREIGN KEY (deleted_image_id) REFERENCES media_object (id)');
        $this->addSql('CREATE INDEX IDX_8045C3893DA5256D ON base_book_nodes (image_id)');
        $this->addSql('CREATE INDEX IDX_8045C389EE29562C ON base_book_nodes (deleted_image_id)');
        $this->addSql('ALTER TABLE project CHANGE error_message error_message TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE code_book_reference_standard CHANGE number_year number_year LONGTEXT NOT NULL');
        $this->addSql('ALTER TABLE code_book_figure ADD image_id INT DEFAULT NULL, ADD deleted_image_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE code_book_figure ADD CONSTRAINT FK_4FC15147EE29562C FOREIGN KEY (deleted_image_id) REFERENCES media_object (id)');
        $this->addSql('ALTER TABLE code_book_figure ADD CONSTRAINT FK_4FC151473DA5256D FOREIGN KEY (image_id) REFERENCES media_object (id)');
        $this->addSql('CREATE INDEX IDX_4FC15147EE29562C ON code_book_figure (deleted_image_id)');
        $this->addSql('CREATE INDEX IDX_4FC151473DA5256D ON code_book_figure (image_id)');
        $this->addSql('ALTER TABLE code_book_link_url DROP FOREIGN KEY FK_9CE163E938B217A7');
        $this->addSql('DROP INDEX IDX_9CE163E938B217A7 ON code_book_link_url');
        $this->addSql('ALTER TABLE code_book_link_url CHANGE publication_id code_book_id INT NOT NULL');
        $this->addSql('ALTER TABLE code_book_link_url ADD CONSTRAINT FK_9CE163E92E05F71D FOREIGN KEY (code_book_id) REFERENCES code_book_publication (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_9CE163E92E05F71D ON code_book_link_url (code_book_id)');
        $this->addSql('ALTER TABLE code_book_audit_log CHANGE changes changes JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE code_book_link_xref DROP FOREIGN KEY FK_8C31F3CB2E05F71D');
        $this->addSql('ALTER TABLE code_book_link_xref ADD CONSTRAINT FK_8C31F3CB2E05F71D FOREIGN KEY (code_book_id) REFERENCES code_book_publication (id)');
    }
}
