<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Disclaimer;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function is_string;

/**
 * @since 1.11
 */
class DisclaimerMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'disclaimer';
    }

    public static function dtoClass(): string
    {
        return Disclaimer::class;
    }

    /** @param Disclaimer $element */
    protected function readElement(Reader $reader, $element): void
    {
        $body = '';

        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif (is_string($value)) {
                $body .= $value;
            }
        }

        $element->setBody($body);
    }

    /** @param Disclaimer $element */
    protected function writeElement(Writer $writer, $element): void
    {
        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->body);
    }
}
