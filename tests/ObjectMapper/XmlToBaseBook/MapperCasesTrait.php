<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook\AbstractBaseBookNode;
use App\Entity\CommonBook;
use App\Serializer\Encoder\Xml2\Element;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\ShortCode;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;

use function DeepCopy\deep_copy;

trait MapperCasesTrait
{
    public function commonAttributeCases(AbstractElement $element, AbstractBaseBookNode $expected): iterable
    {
        $element->setId('id');
        $expected->setNodeId('id');
        yield '@id' => [deep_copy($element), deep_copy($expected)];

        /**
         * @var Element\Attribute\CommonAttributes    $element
         * @var CommonBook\Attribute\CommonAttributes $expected
         */

        $element->setRole('role');
        $expected->setRole('role');
        yield 'common attrs / @role' => [deep_copy($element), deep_copy($expected)];

        $element->setDisplay('display');
        $expected->setDisplay('display');
        yield 'common attrs / @display' => [deep_copy($element), deep_copy($expected)];

        $element->setVerbatim(true);
        $expected->setVerbatim(true);
        yield 'common attrs / @verbatim' => [deep_copy($element), deep_copy($expected)];

        $element->setLanguage('language');
        $expected->setLanguage('language');
        yield 'common attrs / @language' => [deep_copy($element), deep_copy($expected)];

        $element->setAdditionalInfo('additional-info');
        $expected->setAdditionalInfo('additional-info');
        yield 'common attrs / @additional-info' => [deep_copy($element), deep_copy($expected)];
    }

    public function revisionAttributeCases(AbstractElement $element, AbstractBaseBookNode $expected): iterable
    {
        /**
         * @var Element\Attribute\RevisionAttributes    $element
         * @var CommonBook\Attribute\RevisionAttributes $expected
         */

        $element->setRevisionBy('revision-by');
        $expected->setRevisionBy('revision-by');
        yield 'revision attrs / @revision-by' => [deep_copy($element), deep_copy($expected)];

        $element->setRevisionDateTime('1999-12-31T23:59:59+00:00');
        $expected->setRevisionDateTime('1999-12-31T23:59:59+00:00');
        yield 'revision attrs / @revision-datetime' => [deep_copy($element), deep_copy($expected)];

        $element->setRevision('revision');
        $expected->setRevision('revision');
        yield 'revision attrs / @revision' => [deep_copy($element), deep_copy($expected)];

        $element->setRevisionGroup('revision-group');
        $expected->setRevisionGroup('revision-group');
        yield 'revision attrs / @revision-group' => [deep_copy($element), deep_copy($expected)];

        $element->setDataChanged('data-changed');
        $expected->setDataChanged('data-changed');
        yield 'revision attrs / @data-changed' => [deep_copy($element), deep_copy($expected)];

        $element->setDataChangedIn('data-changed-in');
        $expected->setDataChangedIn('data-changed-in');
        yield 'revision attrs / @data-changed-in' => [deep_copy($element), deep_copy($expected)];

        $element->setRelocatedFromAttr('relocated-from');
        $expected->setRelocatedFromAttr('relocated-from');
        yield 'revision attrs / @relocated-from' => [deep_copy($element), deep_copy($expected)];
    }

    public function titleGroupCases(AbstractElement $element, AbstractBaseBookNode $expected): iterable
    {
        /**
         * @var Element\Appendix $element
         * @var CommonBook\Field\TitleGroup $expected
         */

        $titleGroup = new TitleGroup();
        $element->setTitleGroup($titleGroup);

        $superTitle = new SuperTitle();
        $superTitle->setBody('SuperTitle');
        $titleGroup->setSuperTitle($superTitle);
        $expected->setSuperTitle('SuperTitle');
        yield 'titleGroup / superTitle' => [deep_copy($element), deep_copy($expected)];

        $cd = new CommitteeDesignation();
        $cd->setBody('CommitteeDesignation');
        $titleGroup->setCommitteeDesignation($cd);
        $expected->setCommitteeDesignation('CommitteeDesignation');
        yield 'titleGroup / committeeDesignation' => [deep_copy($element), deep_copy($expected)];

        $label = new Label();
        $label->setBody('Label');
        $titleGroup->setLabel($label);
        $expected->setLabel('Label');
        yield 'titleGroup / label' => [deep_copy($element), deep_copy($expected)];

        $number = new Number();
        $number->setBody('Number');
        $titleGroup->setNumber($number);
        $expected->setNumber('Number');
        yield 'titleGroup / number' => [deep_copy($element), deep_copy($expected)];

        $correlated = new Correlated();
        $correlated->setBody('Correlated');
        $titleGroup->setCorrelated($correlated);
        $expected->setCorrelated('Correlated');
        yield 'titleGroup / correlated' => [deep_copy($element), deep_copy($expected)];

        $title = new Title();
        $title->setBody('Title');
        $titleGroup->setTitle($title);
        $expected->setTitle('Title');
        yield 'titleGroup / title' => [deep_copy($element), deep_copy($expected)];

        $title->setTitleAbbreviation('Abbr');
        $expected->setTitleAbbreviation('Abbr');
        yield 'titleGroup / title@abbr' => [deep_copy($element), deep_copy($expected)];

        $title->setTitleYear('1999');
        $expected->setTitleYear('1999');
        yield 'titleGroup / title@year' => [deep_copy($element), deep_copy($expected)];

        $subTitle = new SubTitle();
        $subTitle->setBody('SubTitle');
        $titleGroup->setSubTitle($subTitle);
        $expected->setSubTitle('SubTitle');
        yield 'titleGroup / subTitle' => [deep_copy($element), deep_copy($expected)];

        $history = new History();
        $history->setBody('History');
        $titleGroup->setHistory($history);
        $expected->setTitleHistory('History');
        yield 'titleGroup / titleHistory' => [deep_copy($element), deep_copy($expected)];
    }

    public function qrCodeCases(AbstractElement $element, AbstractBaseBookNode $expected): iterable
    {
        /**
         * @var Element\Appendix $element
         * @var CommonBook\Field\QrCode $expected
         */

        $shortCode = new ShortCode();
        $qrCode = new QrCode();
        $qrCode->setShortCode($shortCode);
        $element->setQrCode($qrCode);

        $qrCode->setId('qrId');
        $expected->setQrId('qrId');
        yield 'qr / @id' => [deep_copy($element), deep_copy($expected)];

        $qrCode->setQrDisplay(true);
        $expected->setQrCodeDisplay(true);
        yield 'qr / @display' => [deep_copy($element), deep_copy($expected)];

        $qrCode->setLevelReference('LevelReference');
        $expected->setQrLevelReference('LevelReference');
        yield 'qr / @level-ref' => [deep_copy($element), deep_copy($expected)];

        $qrCode->setPurpose('Purpose');
        $expected->setQrPurpose('Purpose');
        yield 'qr / @purpose' => [deep_copy($element), deep_copy($expected)];

        $qrCode->setImg('<img src="qr.png" alt=""/>');
        $expected->setQrImage('<img src="qr.png" alt=""/>');
        yield 'qr / img' => [deep_copy($element), deep_copy($expected)];

        $shortCode->setShortUrl('1234567');
        $expected->setQrShortUrl('1234567');
        yield 'qr / short-url' => [deep_copy($element), deep_copy($expected)];

        $qrCode->setBookIcon('<book-icon src="icon.png"/>');
        $expected->setQrBookIcon('<book-icon src="icon.png"/>');
        yield 'qr / book-icon' => [deep_copy($element), deep_copy($expected)];
    }
}
