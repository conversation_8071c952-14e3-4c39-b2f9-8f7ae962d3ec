<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\DefinitionListMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\DefinitionListMapper
 */
class DefinitionListMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(DefinitionList::class, $service->classMap);
        $this->assertIsCallable($service->classMap[DefinitionList::class]);
        $this->assertInstanceOf(DefinitionListMapper::class, $service->classMap[DefinitionList::class][0]);

        $this->assertArrayHasKey(DefinitionListMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[DefinitionListMapper::elementName()]);
        $this->assertInstanceOf(DefinitionListMapper::class, $service->elementMap[DefinitionListMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(DefinitionList $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<def-list %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new DefinitionList();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(DefinitionList $element, string $innerXml): void
    {
        $xml = sprintf('<def-list xmlns="%s" xmlns:m="%s">%s</def-list>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new DefinitionList();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setChildren([new DefinitionItem()]);
        $innerXml .= '<def-item/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $element->setChildren([new DefinitionItem(), new DefinitionItem()]);
        $innerXml .= '<def-item/>';
        yield '2 children' => [deep_copy($element), $innerXml];

        $element->setChildren([new DefinitionItem(), new DefinitionItem(), new RelocatedTo()]);
        $innerXml .= '<relocated-to/>';
        yield 'relocated-to child' => [deep_copy($element), $innerXml];

        $element->setChildren([new DefinitionItem(), new DefinitionItem(),  new RelocatedTo(), new RelocatedFrom()]);
        $innerXml .= '<relocated-from/>';
        yield 'relocated-from child' => [deep_copy($element), $innerXml];
    }
}
