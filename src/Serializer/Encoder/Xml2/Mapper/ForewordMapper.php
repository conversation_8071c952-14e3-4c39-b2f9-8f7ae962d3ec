<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function is_string;
use function mb_trim;
use function preg_replace;

/**
 * @since 1.11
 */
class ForewordMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'foreword';
    }

    public static function dtoClass(): string
    {
        return Foreword::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Foreword);

        if (array_key_exists('tocentry', $attrs) && is_string($attrs['tocentry'])) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Foreword);

        $innerXml = $reader->readInnerXml();
        $innerXml = preg_replace([
            self::REGEX_XMLNS,
            self::REGEX_TITLE_GROUP,
            '/<section\b(?:\s[^>]*)?(?:\/>|>.*?<\/section>)/ms',
        ], '', $innerXml);
        $element->setBody(mb_trim($innerXml));

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('section' === $key && $value instanceof Section) {
                $children[] = $value;
            }
        }
        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Foreword);

        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Foreword);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Section) {
                $writer->writeElement(self::CLARK . 'section', $i);
            }
        }
    }
}
