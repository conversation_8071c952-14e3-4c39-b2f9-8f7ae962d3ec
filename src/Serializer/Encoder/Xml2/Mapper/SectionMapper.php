<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\Enum\Role;
use App\Serializer\Encoder\Xml2\Element\Equation;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function array_merge;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class SectionMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'section';
    }

    public static function dtoClass(): string
    {
        return Section::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Section);

        $element->setRole(Role::SECTION);

        if (array_key_exists('indexnum', $attrs) && is_string($attrs['indexnum'])) {
            $element->setIndexNumber($attrs['indexnum']);
        }
        if (array_key_exists('tocentry', $attrs) && is_string($attrs['tocentry'])) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
        if (array_key_exists('reservecount', $attrs) && is_string($attrs['reservecount'])) {
            $element->setReserveCount($attrs['reservecount']);
        }
        if (array_key_exists('disp-level', $attrs) && is_string($attrs['disp-level'])) {
            $element->setDisplayLevel($attrs['disp-level']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Section);

//        $elementMap = $reader->elementMap;
//        $elementMap[self::CLARK . 'body'] = $elementMap[self::CLARK . 'section-body'];
        $children = [];

        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('abstract' === $key && $value instanceof AbstractField) {
                $element->setAbstract($value);
            } elseif ('keywords' === $key && $value instanceof Keywords) {
                $element->setKeywords($value);
            } elseif ('section-body' === $key && $value instanceof SectionBody) {
                $element->setBody($value);
                $children = array_merge($children, $value->getChildren());
            } elseif ('section' === $key && $value instanceof Section) {
                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Section);

        if (isset($attrs['role']) && $attrs['role'] === 'section') {
            unset($attrs['role']);
        }

        if (!empty($element->getIndexNumber())) {
            $attrs['indexnum'] = $element->getIndexNumber();
        }
        if (!empty($element->hasTocEntry())) {
            $attrs['tocentry'] = 'yes';
        }
        if ($element->getReserveCount() > 0) {
            $attrs['reservecount'] = (string) $element->getReserveCount();
        }
        if ($element->getDisplayLevel() > 0) {
            $attrs['disp-level'] = (string) $element->getDisplayLevel();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Section);

        $bodyChildren = [];
        $children = [];

        foreach ($element->getChildren() as $i) {
            if ($i instanceof DefinitionList || $i instanceof Equation || $i instanceof Figure || $i instanceof Promulgator || $i instanceof Table) {
                $bodyChildren[] = $i;
            } else {
                $children[] = $i;
            }
        }

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if ($element->getAbstract()) {
            $writer->writeElement(self::CLARK . 'abstract', $element->getAbstract());
        }
        if ($element->getKeywords()) {
            $writer->writeElement(self::CLARK . 'keywords', $element->getKeywords());
        }

        if ($element->getBody()) {
            $element->getBody()->setChildren($bodyChildren);
            $writer->writeElement(self::CLARK . 'section-body', $element->getBody());
        }

        foreach ($children as $i) {
            if ($i instanceof Section) {
                $writer->writeElement(self::CLARK . 'section', $i);
            }
        }
    }
}
