<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideChapterMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideChapterMapper */
class XmlOverrideChapterMapperTest extends KernelTestCase
{
    private ?XmlOverrideChapterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideChapterMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Level $element, CodeBook\Chapter $expected): void
    {
        $entity = new CodeBook\Chapter();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Level();
        $expected = new CodeBook\Chapter();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // DisplayLevel
        $element->setDisplayLevel(100);
        $expected->setDisplayLevel(100);
        yield 'displayLevel' => [deep_copy($element), deep_copy($expected)];

        // History
        $history = new History();
        $history->setBody('Chapter history');
        $element->setHistory($history);
        $expected->setHistory('Chapter history');
        yield 'history' => [deep_copy($element), deep_copy($expected)];

        // Objectives
        $body = new Body();
        $body->setBody('<p>Objective Body</p>');
        $objectives = new Objectives();
        $objectives->setBody($body);
        $element->setObjectives($objectives);
        $expected->setObjectives('<p>Objective Body</p>');
        yield 'objectives' => [deep_copy($element), deep_copy($expected)];

        // Objectives + TitleGroup
        $title = new Title();
        $title->setBody('Objectives Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $objectives->setTitleGroup($titleGroup);
        $expected->setObjectivesTitle('Objectives Title');
        yield 'objectivesTitle' => [deep_copy($element), deep_copy($expected)];

        // Abstract
        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        // Abstract + TitleGroup
        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Objectives Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        // Keywords
        $keywords = new Keywords();
        $keywords->setBody('<keyword>Key</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Key</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        // Keywords + TitleGroup
        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Objectives Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        // Body
        $sectionBody = new SectionBody();
        $sectionBody->setBody('<p>Body</p>');
        $element->setBody($sectionBody);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];
    }
}
