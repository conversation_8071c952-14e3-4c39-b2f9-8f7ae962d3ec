<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\Equation;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Table;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function implode;
use function is_string;
use function mb_trim;

class SectionBodyMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'section-body';
    }

    public static function dtoClass(): string
    {
        return SectionBody::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof SectionBody);

        $bodyNodes = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if (is_string($value)) {
                $bodyNodes[] = $value;
            } elseif ('def-list' === $key && $value instanceof DefinitionList) {
                $element->children[] = $value;
            } elseif ('figure' === $key && $value instanceof Figure) {
                $element->children[] = $value;
            } elseif ('promulgator' === $key && $value instanceof Promulgator) {
                $element->children[] = $value;
            } elseif ('formal-table' === $key && $value instanceof Table) {
                $element->children[] = $value;
            } elseif ('equation' === $key && $value instanceof Equation) {
                $element->children[] = $value;
            }
        }

        $element->body = mb_trim(implode('', $bodyNodes));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof SectionBody);

        $cleanedBody = preg_replace('/<equation[^>]*ct-uuid="[^"]*"[^>]*>\s*<\/equation>/', '', $element->getBody());
        $cleanedBody = preg_replace('/<equation[^>]*ct-uuid="[^"]*"[^>]*\/>/', '', $cleanedBody);

        $writer->writeRaw($cleanedBody);

        foreach ($element->getChildren() as $i) {
            match (true) {
                $i instanceof DefinitionList => $writer->writeElement(self::CLARK . 'def-list', $i),
                $i instanceof Equation       => $writer->writeElement(self::CLARK . 'equation', $i),
                $i instanceof Figure         => $writer->writeElement(self::CLARK . 'figure', $i),
                $i instanceof Promulgator    => $writer->writeElement(self::CLARK . 'promulgator', $i),
                $i instanceof Table          => $writer->writeElement(self::CLARK . 'formal-table', $i),
                default                      => null,
            };
        }
    }
}
