<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\PrimaryIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PrimaryIndexEntryMapper
 */
class PrimaryIndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(PrimaryIndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[PrimaryIndexEntry::class]);
        $this->assertInstanceOf(PrimaryIndexEntryMapper::class, $service->classMap[PrimaryIndexEntry::class][0]);

        $this->assertArrayHasKey(PrimaryIndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PrimaryIndexEntryMapper::elementName()]);
        $this->assertInstanceOf(PrimaryIndexEntryMapper::class, $service->elementMap[PrimaryIndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(PrimaryIndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<primaryie %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new PrimaryIndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(PrimaryIndexEntry $element, string $innerXml): void
    {
        $xml = sprintf('<primaryie xmlns="%s" xmlns:m="%s">%s</primaryie>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new PrimaryIndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTerm(new Term());
        $innerXml .= '<term/>';
        yield 'term' => [deep_copy($element), $innerXml];

//        $element->setSeeIndexEntry(new SeeIndexEntry());
//        $innerXml .= '<seeie/>';
//        yield 'seeie' => [deep_copy($element), $innerXml];

//        $element->setSeeAlsoIndexEntry(new SeeAlsoIndexEntry());
//        $innerXml .= '<seealsoie/>';
//        yield 'seealsoie' => [deep_copy($element), $innerXml];

        $element->setNavPointerGroup('<nav-pointer-group/>');
        $innerXml .= '<nav-pointer-group/>';
        yield 'nav-pointer-group' => [deep_copy($element), $innerXml];
    }
}
