<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookPublicationMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\Publication $from): CodeBook\Publication
    {
        $to = new CodeBook\Publication();
        $this->mapCommonAttributes($from, $to);
        $this->mapQrCode($from, $to);

        if ($this->mapper) {
            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }
        }

        return $to;
    }
}
