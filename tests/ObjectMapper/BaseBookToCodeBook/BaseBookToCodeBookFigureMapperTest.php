<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookFigureMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookFigureMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookFigureMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookFigureMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Figure $fromNode, CodeBook\Figure $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Figure();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Figure();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setFloat(FloatEnum::MARGIN);
        $expected->setFloat(FloatEnum::MARGIN);
        yield 'float' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'orientation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setMedia('media');
        $expected->setMedia('media');
        yield 'media' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCaption('caption');
        $expected->setCaption('caption');
        yield 'caption' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setFigureNotesTitle('figureNotesTitle');
        $expected->setFigureNotesTitle('figureNotesTitle');
        yield 'figureNotesTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setFigureNotes('figureNotes');
        $expected->setFigureNotes('figureNotes');
        yield 'figureNotes' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setLegend('legend');
        $expected->setLegend('legend');
        yield 'legend' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setSource('source');
        $expected->setSource('source');
        yield 'source' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCreditTitle('creditTitle');
        $expected->setCreditTitle('creditTitle');
        yield 'creditTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCredit('credit');
        $expected->setCredit('credit');
        yield 'credit' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
