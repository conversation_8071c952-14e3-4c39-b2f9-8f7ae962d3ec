<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToDefinitionListMapper;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToDefinitionListMapper */
class XmlToDefinitionListMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToDefinitionListMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(DefinitionList $element, BaseBook\DefinitionList $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new DefinitionList();
        $expected = new BaseBook\DefinitionList();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setChildren([new DefinitionItem()]);
        $expected->setChildren([new BaseBook\Definition()]);
        yield '1 child' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new DefinitionItem(), new DefinitionItem()]);
        $expected->setChildren([new BaseBook\Definition(), new BaseBook\Definition()]);
        yield '2 children' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new DefinitionItem(), new DefinitionItem(), new RelocatedFrom()]);
        $expected->setChildren([new BaseBook\Definition(), new BaseBook\Definition(), new BaseBook\RelocatedFrom()]);
        yield 'relocated-from child' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new DefinitionItem(), new DefinitionItem(), new RelocatedFrom(), new RelocatedTo()]);
        $expected->setChildren([new BaseBook\Definition(), new BaseBook\Definition(), new BaseBook\RelocatedFrom(), new BaseBook\RelocatedTo()]);
        yield 'relocated-to child' => [deep_copy($element), deep_copy($expected)];
    }
}
