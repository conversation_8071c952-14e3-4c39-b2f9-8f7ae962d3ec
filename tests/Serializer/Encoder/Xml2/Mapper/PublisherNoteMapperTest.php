<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\PublisherNoteMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PublisherNoteMapper
 */
class PublisherNoteMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(PublisherNote::class, $service->classMap);
        $this->assertIsCallable($service->classMap[PublisherNote::class]);
        $this->assertInstanceOf(PublisherNoteMapper::class, $service->classMap[PublisherNote::class][0]);

        $this->assertArrayHasKey(PublisherNoteMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PublisherNoteMapper::elementName()]);
        $this->assertInstanceOf(PublisherNoteMapper::class, $service->elementMap[PublisherNoteMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(PublisherNote $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<publisher-note %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new PublisherNote();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(PublisherNote $element, string $innerXml): void
    {
        $xml = sprintf('<publisher-note xmlns="%s" xmlns:m="%s">%s</publisher-note>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new PublisherNote();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];

        $section = new Section();
        $section->setRole('section');
        $element->setChildren([$section]);
        $innerXml .= '<section disp-level="1"/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $element->setChildren([$section, $section]);
        $innerXml .= '<section disp-level="1"/>';
        yield '2 children' => [deep_copy($element), $innerXml];
    }
}
