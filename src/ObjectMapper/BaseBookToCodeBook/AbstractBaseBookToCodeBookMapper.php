<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook\AbstractBaseBookNode;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CommonBook\Attribute\RevisionAttributes;
use App\Entity\CommonBook\Field\QrCode;
use App\Entity\CommonBook\Field\TitleGroup;
use App\ObjectMapper\BaseBookToCodeBookMapper;
use Symfony\Contracts\Service\Attribute\Required;

use function method_exists;

abstract class AbstractBaseBookToCodeBookMapper
{
    protected ?BaseBookToCodeBookMapper $mapper;

    /** Injects service without causing circular dependency issues */
    #[Required]
    public function setBaseBookToCodeBookMapper(?BaseBookToCodeBookMapper $mapper): void
    {
        $this->mapper = $mapper;
    }

    protected function mapCommonAttributes(AbstractBaseBookNode $from, AbstractCodeBookNode $to): void
    {
        // basic attributes
        $to->setOriginalNodeId($from->getNodeId());
        $to->setPosition($from->getPosition());

        $to->setRole($from->getRole());
        $to->setDisplay($from->getDisplay());
        $to->setVerbatim($from->isVerbatim());
        $to->setLanguage($from->getLanguage());
        $to->setAdditionalInfo($from->getAdditionalInfo());
    }

    protected function mapRevisionAttributes(AbstractBaseBookNode $from, AbstractCodeBookNode $to): void
    {
        /** @var RevisionAttributes $from  */
        /** @var RevisionAttributes $to  */
        $to->setRevisionBy($from->getRevisionBy());
        $to->setRevisionDateTime($from->getRevisionDateTime());
        $to->setRevision($from->getRevision());
        $to->setRevisionGroup($from->getRevisionGroup());
        $to->setDataChanged($from->getDataChanged());
        $to->setDataChangedIn($from->getDataChangedIn());
        $to->setRelocatedFromAttr($from->getRelocatedFromAttr());
    }

    protected function mapTitleGroup(AbstractBaseBookNode $from, AbstractCodeBookNode $to): void
    {
        /** @var TitleGroup $from */
        /** @var TitleGroup $to */
        $to->setSuperTitle($from->getSuperTitle());
        $to->setCommitteeDesignation($from->getCommitteeDesignation());
        $to->setLabel($from->getLabel());
        $to->setNumber($from->getNumber());
        $to->setCorrelated($from->getCorrelated());
        $to->setTitle($from->getTitle());
        $to->setTitleAbbreviation($from->getTitleAbbreviation());
        $to->setTitleYear($from->getTitleYear());
        $to->setSubTitle($from->getSubTitle());
        $to->setTitleHistory($from->getTitleHistory());
    }

    protected function mapQrCode(AbstractBaseBookNode $from, AbstractCodeBookNode $to): void
    {
        /** @var QrCode $from */
        /** @var QrCode $to */
        $to->setQrActive($from->isQrActive());
        $to->setQrDisplay($from->isQrDisplay());
        $to->setQrLevelReference($from->getQrLevelReference());
        $to->setQrPurpose($from->getQrPurpose());
        $to->setQrImage($from->getQrImage());
        $to->setQrBookIcon($from->getQrBookIcon());
        $to->setQrUrl($from->getQrUrl());
        $to->setQrIcon($from->getQrIcon());
        $to->setQrDepartment($from->getQrDepartment());
        $to->setQrBusinessUnit($from->getQrBusinessUnit());
    }
}
