<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\Term;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\keyValue;

class DefinitionItemMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'def-item';
    }

    public static function dtoClass(): string
    {
        return DefinitionItem::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof DefinitionItem);

        if (array_key_exists('indexnum', $attrs) && is_string($attrs['indexnum'])) {
            $element->setIndexNumber($attrs['indexnum']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof DefinitionItem);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('committee-desig' === $key && $value instanceof CommitteeDesignation) {
                $element->setCommitteeDesignation($value);
            } elseif ('term' === $key && $value instanceof Term) {
                $element->setTerm($value);
            } elseif ('def' === $key && $value instanceof Definition) {
                $element->setDefinition($value);
            }
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof DefinitionItem);

        if (!empty($element->getIndexNumber())) {
            $attrs['indexnum'] = $element->getIndexNumber();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof DefinitionItem);

        if ($element->getCommitteeDesignation()) {
            $writer->writeElement(self::CLARK . 'committee-desig', $element->getCommitteeDesignation());
        }
        if ($element->getTerm()) {
            $writer->writeElement(self::CLARK . 'term', $element->getTerm());
        }
        if ($element->getDefinition()) {
            $writer->writeElement(self::CLARK . 'def', $element->getDefinition());
        }
    }
}
