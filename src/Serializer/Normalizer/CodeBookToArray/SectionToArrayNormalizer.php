<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Section;
use App\Serializer\Dto\Book\SectionDto;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use App\Service\Xml2\EquationService;
use Symfony\Contracts\Service\Attribute\Required;

class SectionToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    #[Required]
    public EquationService $equationService;

    public function getSupportedTypes(?string $format): array
    {
        return [
            Section::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Section
            && 'json' === $format;
    }

    /** @param Section $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new SectionDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapTitleGroup($object, $dto);
        $this->mapQrCode($object, $dto);

        $dto->setIndexNumber($object->getIndexNumber());
        $dto->setTocEntry($object->hasTocEntry());
        $dto->setReserveCount($object->getReserveCount());
        $dto->setDisplayLevel($object->getDisplayLevel());

        $dto->setHistory($object->getHistory());
        $dto->setObjectivesTitle($object->getObjectivesTitle());
        $dto->setObjectives($object->getObjectives());
        $dto->setAbstractTitle($object->getAbstractTitle());
        $dto->setAbstract($object->getAbstract());
        $dto->setKeywordsTitle($object->getKeywordsTitle());
        $dto->setKeywords($object->getKeywords());

        $body = $this->equationService->processEquationEntityInSection($object->getBody());
        $dto->setBody($body);

        try {
            $skip = $context['bookContents'] ?? false;
            if (!$skip) {
                $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
                $dto->setChildren($children);
            }
        } catch (ExceptionInterface $exception) {
        }

        return $dto->asArray();
    }
}
