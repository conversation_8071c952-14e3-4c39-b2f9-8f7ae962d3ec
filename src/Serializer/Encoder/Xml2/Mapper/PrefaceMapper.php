<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractBlockElement;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function is_string;
use function mb_trim;

/**
 * @since 1.11
 */
class PrefaceMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'preface';
    }

    public static function dtoClass(): string
    {
        return Preface::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Preface);

        if (array_key_exists('tocentry', $attrs) && is_string($attrs['tocentry'])) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Preface);

        $body = '';
        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('section' === $key && $value instanceof Section) {
                $children[] = $value;
            } elseif (is_string($value)) {
                $body .= $value . "\n";
            } elseif ($value instanceof AbstractBlockElement) {
                $body .= $value->getOuterXml() . "\n";
            }
        }

        $element->setBody(mb_trim($body));
        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Preface);

        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Preface);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Section) {
                $writer->writeElement(self::CLARK . 'section', $i);
            }
        }
    }
}
