<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverridePromulgatorMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Promulgator $element, CodeBook\Promulgator $entity): CodeBook\Promulgator
    {
        if ($acronym = $element->getAcronym()) {
            $entity->setAcronym($acronym->getBody());
        }

        if ($address = $element->getAddress()) {
            $entity->setAddressLine($address->getAddressLine());
            $entity->setOrganizationName($address->getOrganizationName());
            $entity->setStreet($address->getStreet());
            $entity->setCity($address->getCity());
            $entity->setState($address->getState());
            $entity->setPostalCode($address->getPostalCode());
            $entity->setCountry($address->getCountry());
            $entity->setPhone($address->getPhone());
            $entity->setFax($address->getFax());

            if ($email = $address->getEmail()) {
                $entity->setEmail($email->getBody());
            }
            if ($url = $address->getUrl()) {
                $entity->setUrl($url->getBody());
                $entity->setUrlHref($url->getHref());
                $entity->setUrlAlt($url->getAlt());
            }
        }

        if ($references = $element->getReferences()) {
            $children = array_map(fn($i) => $this->mapper->map($i), $references->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
