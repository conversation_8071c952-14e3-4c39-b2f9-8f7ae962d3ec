<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Figure;
use App\Serializer\Dto\Book\FigureDto;

class FigureToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Figure::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Figure
            && 'json' === $format;
    }

    /** @param Figure $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new FigureDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapTitleGroup($object, $dto);

        $dto->setTocEntry($object->hasTocEntry());
        $dto->setFloat($object->getFloat());
        $dto->setOrientation($object->getOrientation());

        $dto->setMedia($object->getMedia());
        $dto->setCaption($object->getCaption());
        $dto->setFigureNotesTitle($object->getFigureNotesTitle());
        $dto->setFigureNotes($object->getFigureNotes());
        $dto->setLegend($object->getLegend());
        $dto->setSource($object->getSource());
        $dto->setCreditTitle($object->getCreditTitle());
        $dto->setCredit($object->getCredit());
        $dto->setQaCheck($object->isQaCheck());

        return $dto->asArray();
    }
}
