<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookPromulgatorMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookPromulgatorMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookPromulgatorMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookPromulgatorMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Promulgator $fromNode, CodeBook\Promulgator $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Promulgator();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Promulgator();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setAcronym('acronym');
        $expected->setAcronym('acronym');
        yield 'acronym' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setAddressLine('addressLine');
        $expected->setAddressLine('addressLine');
        yield 'addressLine' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setOrganizationName('organizationName');
        $expected->setOrganizationName('organizationName');
        yield 'organizationName' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setStreet('street');
        $expected->setStreet('street');
        yield 'street' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCity('city');
        $expected->setCity('city');
        yield 'city' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setState('state');
        $expected->setState('state');
        yield 'state' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setPostalCode('postalCode');
        $expected->setPostalCode('postalCode');
        yield 'postalCode' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCountry('country');
        $expected->setCountry('country');
        yield 'country' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setUrl('url');
        $expected->setUrl('url');
        yield 'url' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setUrlHref('url#href');
        $expected->setUrlHref('url#href');
        yield 'url#href' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setUrlAlt('url#alt');
        $expected->setUrlAlt('url#alt');
        yield 'url#alt' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setEmail('email');
        $expected->setEmail('email');
        yield 'email' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setPhone('phone');
        $expected->setPhone('phone');
        yield 'phone' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setFax('fax');
        $expected->setFax('fax');
        yield 'fax' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setInternational(true);
        $expected->setInternational(true);
        yield 'international' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setChildren([new BaseBook\ReferenceStandard(), new BaseBook\ReferenceStandard()]);
        $expected->setChildren([new CodeBook\ReferenceStandard(), new CodeBook\ReferenceStandard()]);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
