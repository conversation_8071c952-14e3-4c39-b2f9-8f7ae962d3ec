<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class PrimaryIndexEntry extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\ReferenceId;

    public ?Term $term = null;
    public ?SeeIndexEntry $seeIndexEntry = null;
    public ?SeeAlsoIndexEntry $seeAlsoIndexEntry = null;
    public string $navPointerGroup = '';

    public function getTerm(): ?Term
    {
        return $this->term;
    }

    public function setTerm(?Term $term): void
    {
        $this->term = $term;
    }

    public function getSeeIndexEntry(): ?SeeIndexEntry
    {
        return $this->seeIndexEntry;
    }

    public function setSeeIndexEntry(?SeeIndexEntry $seeIndexEntry): void
    {
        $this->seeIndexEntry = $seeIndexEntry;
    }

    public function getSeeAlsoIndexEntry(): ?SeeAlsoIndexEntry
    {
        return $this->seeAlsoIndexEntry;
    }

    public function setSeeAlsoIndexEntry(?SeeAlsoIndexEntry $seeAlsoIndexEntry): void
    {
        $this->seeAlsoIndexEntry = $seeAlsoIndexEntry;
    }

    public function getNavPointerGroup(): string
    {
        return $this->navPointerGroup;
    }

    public function setNavPointerGroup(string $navPointerGroup): void
    {
        $this->navPointerGroup = $navPointerGroup;
    }
}
