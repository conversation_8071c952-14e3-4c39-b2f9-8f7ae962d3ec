<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTitlePageMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlTitlePageMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlTitlePageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlTitlePageMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\TitlePage $from, Xml2\TitlePage $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\TitlePage();
        $from->setNodeId('');
        $from->setRole('');
        $expected = new Xml2\TitlePage();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
    }
}
