<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideTitlePageMapper;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideTitlePageMapper */
class XmlOverrideTitlePageMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideTitlePageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideTitlePageMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(TitlePage $element, CodeBook\TitlePage $expected): void
    {
        $entity = new CodeBook\TitlePage();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new TitlePage();
        $expected = new CodeBook\TitlePage();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case: nothing set
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        yield from $this->titleGroupCases($element, $expected);
    }
}
