<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

use App\Enum\GoverningType;
use App\Enum\TitleType;

/**
 * @since 1.11
 */
class Volume extends AbstractElement
{
    use Attribute\CommonAttributes;
    use HasChildren;

    use Field\TitleGroupTrait;
    public string $customerId = '';
    public string $titleType = TitleType::NONE;
    public string $governingType = GoverningType::NONE;
    public ?Metadata $metadata = null;
    public ?FrontMatter $frontMatter = null;
    public ?BackMatter $backMatter = null;

    public function getCustomerId(): string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): void
    {
        $this->customerId = $customerId;
    }

    public function getTitleType(): string
    {
        return $this->titleType;
    }

    public function setTitleType(string $titleType): void
    {
        $this->titleType = $titleType;
    }

    public function getGoverningType(): string
    {
        return $this->governingType;
    }

    public function setGoverningType(string $governingType): void
    {
        $this->governingType = $governingType;
    }

    public function getMetadata(): ?Metadata
    {
        return $this->metadata;
    }

    public function setMetadata(?Metadata $metadata): void
    {
        $this->metadata = $metadata;
    }

    public function getFrontMatter(): ?FrontMatter
    {
        return $this->frontMatter;
    }

    public function setFrontMatter(?FrontMatter $frontMatter): void
    {
        $this->frontMatter = $frontMatter;
    }

    public function getBackMatter(): ?BackMatter
    {
        return $this->backMatter;
    }

    public function setBackMatter(?BackMatter $backMatter): void
    {
        $this->backMatter = $backMatter;
    }
}
