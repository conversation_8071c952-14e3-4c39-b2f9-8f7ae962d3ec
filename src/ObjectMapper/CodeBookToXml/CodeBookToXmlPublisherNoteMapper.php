<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlPublisherNoteMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\PublisherNote $from): Xml2\PublisherNote
    {
        $to = new Xml2\PublisherNote();
        $this->mapCommonAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        $to->setTocEntry($from->hasTocEntry());
        $to->setBody($from->getBody());

        $children = [];
        foreach ($from->getChildren() as $child) {
            $children[] = $this->mapper->map($child);
        }
        $to->setChildren($children);

        return $to;
    }
}
