<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Serializer\Encoder\Xml2\Element\Enum\Role;

/**
 * @since 1.11
 */
trait CommonAttributes
{
    public string $role = Role::NONE;
    public string $display = '';
    public bool $verbatim = false;
    public string $language = '';
    public string $additionalInfo = '';

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getDisplay(): string
    {
        return $this->display;
    }

    public function setDisplay(string $display): void
    {
        $this->display = $display;
    }

    public function isVerbatim(): bool
    {
        return $this->verbatim;
    }

    public function setVerbatim(bool $verbatim): void
    {
        $this->verbatim = $verbatim;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): void
    {
        $this->language = $language;
    }

    public function getAdditionalInfo(): string
    {
        return $this->additionalInfo;
    }

    public function setAdditionalInfo(string $additionalInfo): void
    {
        $this->additionalInfo = $additionalInfo;
    }
}
