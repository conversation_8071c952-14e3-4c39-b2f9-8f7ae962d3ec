<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\Align;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedFromMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlRelocatedFromMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlRelocatedFromMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlRelocatedFromMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\RelocatedFrom $from, Xml2\RelocatedFrom $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\RelocatedFrom();
        $from->setNodeId('');
        $expected = new Xml2\RelocatedFrom();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);

        $from->setAlign(Align::LEFT);
        $expected->setAlign(Align::LEFT);
        yield 'align' => [deep_copy($from), deep_copy($expected)];

        $from->setIndent(true);
        $expected->setIndent(true);
        yield 'indent' => [deep_copy($from), deep_copy($expected)];

        $from->setReferenceId('referenceId');
        $expected->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($from), deep_copy($expected)];

        $from->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($from), deep_copy($expected)];
    }
}
