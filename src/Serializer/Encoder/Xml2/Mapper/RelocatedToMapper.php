<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\RelocatedTo;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class RelocatedToMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'relocated-to';
    }

    public static function dtoClass(): string
    {
        return RelocatedTo::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof RelocatedTo);

        if (array_key_exists('relocated-to', $attrs) && is_string($attrs['relocated-to'])) {
            $element->setRelocatedTo($attrs['relocated-to']);
        }
        if (array_key_exists('align', $attrs) && is_string($attrs['align'])) {
            $element->setAlign($attrs['align']);
        }
        if (array_key_exists('rid', $attrs) && is_string($attrs['rid'])) {
            $element->setReferenceId($attrs['rid']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof RelocatedTo);

        if (!empty($element->getRelocatedTo())) {
            $attrs['relocated-to'] = $element->getRelocatedTo();
        }
        if (!empty($element->getAlign()) && 'left' !== $element->getAlign()) {
            $attrs['align'] = $element->getAlign();
        }
        if (!empty($element->getReferenceId())) {
            $attrs['rid'] = $element->getReferenceId();
        }
    }
}
