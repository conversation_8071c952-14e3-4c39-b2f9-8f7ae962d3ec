<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Acronym;
use App\Serializer\Encoder\Xml2\Element\Address;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\References;
use App\Serializer\Encoder\Xml2\Mapper\PromulgatorMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PromulgatorMapper
 */
class PromulgatorMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Promulgator::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Promulgator::class]);
        $this->assertInstanceOf(PromulgatorMapper::class, $service->classMap[Promulgator::class][0]);

        $this->assertArrayHasKey(PromulgatorMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PromulgatorMapper::elementName()]);
        $this->assertInstanceOf(PromulgatorMapper::class, $service->elementMap[PromulgatorMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Promulgator $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<promulgator %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Promulgator();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Promulgator $element, string $innerXml): void
    {
        $xml = sprintf('<promulgator xmlns="%s" xmlns:m="%s">%s</promulgator>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Promulgator();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setAcronym(new Acronym());
        $innerXml .= '<acronym/>';
        yield 'acronym' => [deep_copy($element), $innerXml];

        $element->setAddress(new Address());
        $innerXml .= '<address/>';
        yield 'address' => [deep_copy($element), $innerXml];

        $element->setReferences(new References());
        $innerXml .= '<references/>';
        yield 'references' => [deep_copy($element), $innerXml];
    }
}
