<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideIndexEntryMapper */
class XmlOverrideIndexEntryMapperTest extends KernelTestCase
{
    private ?XmlOverrideIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideIndexEntryMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(IndexEntry $element, CodeBook\IndexEntry $expected): void
    {
        $entity = new CodeBook\IndexEntry();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new IndexEntry();
        $expected = new CodeBook\IndexEntry();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // PrimaryIndexEntry with only referenceId
        $primary = new PrimaryIndexEntry();
        $primary->setReferenceId('ref-001');
        $element->setPrimaryIndexEntry($primary);

        $expected->setReferenceId('ref-001');
        yield 'primaryIndexEntry-referenceId' => [deep_copy($element), deep_copy($expected)];

        // PrimaryIndexEntry with term
        $term = new Term();
        $term->setBody('Sample Term');
        $primary->setTerm($term);
        $element->setPrimaryIndexEntry($primary);

        $expected->setReferenceId('ref-001');
        $expected->setTerm('Sample Term');
        yield 'primaryIndexEntry-term' => [deep_copy($element), deep_copy($expected)];
    }
}
