<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class FrontMatter extends AbstractElement
{
    use Attribute\CommonAttributes;
    use HasChildren;

    public ?TitlePage $titlePage = null;
    public ?CopyrightPage $copyrightPage = null;
    public ?PublisherNote $publisherNote = null;
    public ?Preface $preface = null;
    public ?Foreword $foreword = null;

    public function getTitlePage(): ?TitlePage
    {
        return $this->titlePage;
    }

    public function setTitlePage(?TitlePage $titlePage): void
    {
        $this->titlePage = $titlePage;
    }

    public function getCopyrightPage(): ?CopyrightPage
    {
        return $this->copyrightPage;
    }

    public function setCopyrightPage(?CopyrightPage $copyrightPage): void
    {
        $this->copyrightPage = $copyrightPage;
    }

    public function getPublisherNote(): ?PublisherNote
    {
        return $this->publisherNote;
    }

    public function setPublisherNote(?PublisherNote $publisherNote): void
    {
        $this->publisherNote = $publisherNote;
    }

    public function getPreface(): ?Preface
    {
        return $this->preface;
    }

    public function setPreface(?Preface $preface): void
    {
        $this->preface = $preface;
    }

    public function getForeword(): ?Foreword
    {
        return $this->foreword;
    }

    public function setForeword(?Foreword $foreword): void
    {
        $this->foreword = $foreword;
    }
}
