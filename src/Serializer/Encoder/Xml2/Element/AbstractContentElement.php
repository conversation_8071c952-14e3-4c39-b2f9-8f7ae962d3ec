<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

use function mb_trim;

/**
 * @since 1.11
 */
abstract class AbstractContentElement extends AbstractElement
{
    public string $body = '';

    public function __construct(string $body = '')
    {
        $this->setBody($body);
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): void
    {
        $this->body = mb_trim($body);
    }
}
