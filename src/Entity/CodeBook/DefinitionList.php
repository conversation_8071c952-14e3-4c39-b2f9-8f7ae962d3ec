<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\CodeBook;

use App\Entity\BaseBook\AbstractBaseBookNode;
use App\Entity\CommonBook\DefinitionListFields;
use App\Traits\NumberCleanerTrait;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'code_book_definition_list')]
class DefinitionList extends AbstractCodeBookNode
{
    use NumberCleanerTrait;
    use DefinitionListFields;

    public function getChildren(bool $withDeleted = true): array
    {

        $list = parent::getChildren($withDeleted);
        return $this->sortDefinitions($list);
    }

    private function sortDefinitions(array $definitions): array
    {
        usort($definitions, function ($a, $b) {
            if ($a instanceof Definition && $b instanceof Definition) {
                $termA = strtoupper(trim($this->extractCleanNumber($a->getTerm())));
                $termB = strtoupper(trim($this->extractCleanNumber($b->getTerm())));
                return strcmp($termA, $termB);
            }

            return 0;
        });

        return $definitions;
    }
}
