<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPromulgatorMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlPromulgatorMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlPromulgatorMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlPromulgatorMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Promulgator $from, Xml2\Promulgator $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Promulgator();
        $from->setNodeId('');
        $expected = new Xml2\Promulgator();
        $address = new Xml2\Address();
        $expected->setAddress($address);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);

        $from->setAcronym('acronym');
        $acronym = new Xml2\Acronym();
        $acronym->setBody('acronym');
        $expected->setAcronym($acronym);
        yield 'acronym' => [deep_copy($from), deep_copy($expected)];

        $from->setAddressLine('addressLine');
        $address->setAddressLine('addressLine');
        yield 'addressLine' => [deep_copy($from), deep_copy($expected)];

        $from->setOrganizationName('organizationName');
        $address->setOrganizationName('organizationName');
        yield 'organizationName' => [deep_copy($from), deep_copy($expected)];

        $from->setStreet('street');
        $address->setStreet('street');
        yield 'street' => [deep_copy($from), deep_copy($expected)];

        $from->setCity('city');
        $address->setCity('city');
        yield 'city' => [deep_copy($from), deep_copy($expected)];

        $from->setState('state');
        $address->setState('state');
        yield 'state' => [deep_copy($from), deep_copy($expected)];

        $from->setPostalCode('postalCode');
        $address->setPostalCode('postalCode');
        yield 'postalCode' => [deep_copy($from), deep_copy($expected)];

        $from->setCountry('country');
        $address->setCountry('country');
        yield 'country' => [deep_copy($from), deep_copy($expected)];

        $from->setUrl('url');
        $url = new Xml2\Url();
        $url->setBody('url');
        $address->setUrl($url);
        yield 'url' => [deep_copy($from), deep_copy($expected)];

        $from->setUrlHref('urlHref');
        $url->setHref('urlHref');
        yield 'urlHref' => [deep_copy($from), deep_copy($expected)];

        $from->setUrlAlt('urlAlt');
        $url->setAlt('urlAlt');
        yield 'urlAlt' => [deep_copy($from), deep_copy($expected)];

        $from->setEmail('email');
        $email = new Xml2\Email();
        $email->setBody('email');
        $address->setEmail($email);
        yield 'email' => [deep_copy($from), deep_copy($expected)];

        $from->setPhone('phone');
        $address->setPhone('phone');
        yield 'phone' => [deep_copy($from), deep_copy($expected)];

        $from->setFax('fax');
        $address->setFax('fax');
        yield 'fax' => [deep_copy($from), deep_copy($expected)];
    }
}
