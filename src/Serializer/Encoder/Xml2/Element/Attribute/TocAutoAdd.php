<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

/**
 * @since 1.11
 */
trait TocAutoAdd
{
    public bool $tocAutoAdd = false;

    public function isTocAutoAdd(): bool
    {
        return $this->tocAutoAdd;
    }

    public function setTocAutoAdd(bool $tocAutoAdd): void
    {
        $this->tocAutoAdd = $tocAutoAdd;
    }
}
