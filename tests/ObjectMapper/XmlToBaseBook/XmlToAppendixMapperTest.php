<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToAppendixMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToAppendixMapper */
class XmlToAppendixMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToAppendixMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToAppendixMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Appendix $element, BaseBook\Appendix $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Appendix();
        $expected = new BaseBook\Appendix();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);
        yield from $this->qrCodeCases($element, $expected);

        $element->setIndexNumber('index-number');
        $expected->setIndexNumber('index-number');
        yield '@index-number' => [deep_copy($element), deep_copy($expected)];

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setTocAutoAdd(true);
        $expected->setTocAutoAdd(true);
        yield '@toc-auto-add' => [deep_copy($element), deep_copy($expected)];

        $history = new History();
        $history->setBody('History');
        $element->setHistory($history);
        $expected->setHistory('History');
        yield 'history' => [deep_copy($element), deep_copy($expected)];

        $body = new Body();
        $body->setBody('<p>Body</p>');

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $objectives = new Objectives();
        $objectives->setBody($body);
        $element->setObjectives($objectives);
        $expected->setObjectives('<p>Body</p>');
        yield 'objectives' => [deep_copy($element), deep_copy($expected)];

        $objectives->setTitleGroup($titleGroup);
        $expected->setObjectivesTitle('Title');
        yield 'objectivesTitle' => [deep_copy($element), deep_copy($expected)];

        $note = new Note();
        $note->setBody('<p>Note</p>');
        $element->setNote($note);
        $expected->setNote('<p>Note</p>');
        yield 'note' => [deep_copy($element), deep_copy($expected)];

        $note->setTitleGroup($titleGroup);
        $expected->setNoteTitle('Title');
        yield 'noteTitle' => [deep_copy($element), deep_copy($expected)];

        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        $keywords = new Keywords();
        $keywords->setBody('<keyword>Keyword</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Keyword</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Section()]);
        $expected->setChildren([new BaseBook\Section()]);
        yield '1 child' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Section(), new Section()]);
        $expected->setChildren([new BaseBook\Section(), new BaseBook\Section()]);
        yield '2 children' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Section(), new Section(), new RelocatedTo()]);
        $expected->setChildren([new BaseBook\Section(), new BaseBook\Section(), new BaseBook\RelocatedTo()]);
        yield 'relocated-to child' => [deep_copy($element), deep_copy($expected)];
    }
}
