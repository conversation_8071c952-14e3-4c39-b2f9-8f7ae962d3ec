<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Equation;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Preface;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\RelocatedFrom;
use App\Entity\CodeBook\RelocatedTo;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\CodeBook\TitlePage;
use App\Entity\CodeBook\Volume;
use App\Exception\ApiException;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlAppendixMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlBackMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlChapterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlCopyrightPageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionListMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlEquationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFigureMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlForewordMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFrontMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexDivisionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPrefaceMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPromulgatorMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublicationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublisherNoteMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlReferenceStandardMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedFromMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedToMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSecondaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSectionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTableMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTertiaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTitlePageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlVolumeMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;

use function get_class;
use function sprintf;

class CodeBookToXmlMapper
{
    public function __construct(
        private readonly CodeBookToXmlAppendixMapper            $appendixMapper,
        private readonly CodeBookToXmlBackMatterMapper          $backMatterMapper,
        private readonly CodeBookToXmlChapterMapper             $chapterMapper,
        private readonly CodeBookToXmlCopyrightPageMapper       $copyrightPageMapper,
        private readonly CodeBookToXmlDefinitionMapper          $definitionMapper,
        private readonly CodeBookToXmlDefinitionListMapper      $definitionListMapper,
        private readonly CodeBookToXmlEquationMapper            $equationMapper,
        private readonly CodeBookToXmlFigureMapper              $figureMapper,
        private readonly CodeBookToXmlForewordMapper            $forewordMapper,
        private readonly CodeBookToXmlFrontMatterMapper         $frontMatterMapper,
        private readonly CodeBookToXmlIndexMapper               $indexMapper,
        private readonly CodeBookToXmlIndexDivisionMapper       $indexDivisionMapper,
        private readonly CodeBookToXmlIndexEntryMapper          $indexEntryMapper,
        private readonly CodeBookToXmlPrefaceMapper             $prefaceMapper,
        private readonly CodeBookToXmlPromulgatorMapper         $promulgatorMapper,
        private readonly CodeBookToXmlPublicationMapper         $publicationMapper,
        private readonly CodeBookToXmlPublisherNoteMapper       $publisherNoteMapper,
        private readonly CodeBookToXmlReferenceStandardMapper   $referenceStandardMapper,
        private readonly CodeBookToXmlRelocatedFromMapper       $relocatedFromMapper,
        private readonly CodeBookToXmlRelocatedToMapper         $relocatedToMapper,
        private readonly CodeBookToXmlSecondaryIndexEntryMapper $secondaryIndexEntryMapper,
        private readonly CodeBookToXmlSectionMapper             $sectionMapper,
        private readonly CodeBookToXmlTableMapper               $tableMapper,
        private readonly CodeBookToXmlTertiaryIndexEntryMapper  $tertiaryIndexEntryMapper,
        private readonly CodeBookToXmlTitlePageMapper           $titlePageMapper,
        private readonly CodeBookToXmlVolumeMapper              $volumeMapper,
    ) {
    }

    /** @throws ApiException */
    public function map(AbstractCodeBookNode $from): AbstractElement
    {
        $node = match (true) {
            $from instanceof Appendix            => $this->appendixMapper->map($from),
            $from instanceof BackMatter          => $this->backMatterMapper->map($from),
            $from instanceof Chapter             => $this->chapterMapper->map($from),
            $from instanceof CopyrightPage       => $this->copyrightPageMapper->map($from),
            $from instanceof Definition          => $this->definitionMapper->map($from),
            $from instanceof DefinitionList      => $this->definitionListMapper->map($from),
            $from instanceof Equation            => $this->equationMapper->map($from),
            $from instanceof Figure              => $this->figureMapper->map($from),
            $from instanceof Foreword            => $this->forewordMapper->map($from),
            $from instanceof FrontMatter         => $this->frontMatterMapper->map($from),
            $from instanceof Index               => $this->indexMapper->map($from),
            $from instanceof IndexDivision       => $this->indexDivisionMapper->map($from),
            $from instanceof IndexEntry          => $this->indexEntryMapper->map($from),
            $from instanceof Preface             => $this->prefaceMapper->map($from),
            $from instanceof Promulgator         => $this->promulgatorMapper->map($from),
            $from instanceof Publication         => $this->publicationMapper->map($from),
            $from instanceof PublisherNote       => $this->publisherNoteMapper->map($from),
            $from instanceof ReferenceStandard   => $this->referenceStandardMapper->map($from),
            $from instanceof RelocatedFrom       => $this->relocatedFromMapper->map($from),
            $from instanceof RelocatedTo         => $this->relocatedToMapper->map($from),
            $from instanceof SecondaryIndexEntry => $this->secondaryIndexEntryMapper->map($from),
            $from instanceof Section             => $this->sectionMapper->map($from),
            $from instanceof Table               => $this->tableMapper->map($from),
            $from instanceof TertiaryIndexEntry  => $this->tertiaryIndexEntryMapper->map($from),
            $from instanceof TitlePage           => $this->titlePageMapper->map($from),
            $from instanceof Volume              => $this->volumeMapper->map($from),
            default                              => null,
        };

        if (null === $node) {
            throw new ApiException(sprintf('%s is not a supported format', get_class($from)));
        }

        return $node;
    }
}
