<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function assert;

class CodeBookToXmlFrontMatterMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\FrontMatter $from): Xml2\FrontMatter
    {
        $to = new Xml2\FrontMatter();
        $this->mapCommonAttributes($from, $to);

        if ($from->getTitlePage()) {
            $titlePage = $this->mapper->map($from->getTitlePage());
            assert($titlePage instanceof Xml2\TitlePage);
            $to->setTitlePage($titlePage);
        }

        if ($from->getCopyrightPage()) {
            $copyrightPage = $this->mapper->map($from->getCopyrightPage());
            assert($copyrightPage instanceof Xml2\CopyrightPage);
            $to->setCopyrightPage($copyrightPage);
        }

        if ($from->getPublisherNote()) {
            $publisherNote = $this->mapper->map($from->getPublisherNote());
            assert($publisherNote instanceof Xml2\PublisherNote);
            $to->setPublisherNote($publisherNote);
        }

        if ($from->getPreface()) {
            $preface = $this->mapper->map($from->getPreface());
            assert($preface instanceof Xml2\Preface);
            $to->setPreface($preface);
        }

        if ($from->getForeword()) {
            $foreword = $this->mapper->map($from->getForeword());
            assert($foreword instanceof Xml2\Foreword);
            $to->setForeword($foreword);
        }

        if (!empty($from->getSections())) {
            $children = [];
            foreach ($from->getSections() as $i) {
                $section = $this->mapper->map($i);
                assert($section instanceof Xml2\Section);
                $children[] = $section;
            }
            $to->setChildren($children);
        }

        return $to;
    }
}
