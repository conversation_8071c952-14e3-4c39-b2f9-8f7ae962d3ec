<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\keyValue;

/**
 * @since 1.11
 */
class ObjectivesMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'objectives';
    }

    public static function dtoClass(): string
    {
        return Objectives::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Objectives);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('body' === $key && $value instanceof Body) {
                $element->setBody($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Objectives);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        if ($element->getBody()) {
            $writer->writeElement(self::CLARK . 'body', $element->getBody());
        }
    }
}
