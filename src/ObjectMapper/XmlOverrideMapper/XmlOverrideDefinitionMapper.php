<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideDefinitionMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\DefinitionItem $element, CodeBook\Definition $entity): CodeBook\Definition
    {
        if ($cd = $element->getCommitteeDesignation()) {
            $entity->setCommitteeDesignation($cd->getBody());
        }
        if ($term = $element->getTerm()) {
            $entity->setTerm($term->getBody());
        }
        if ($definition = $element->getDefinition()) {
            $entity->setDefinition($definition->getBody());
        }

        return $entity;
    }
}
