<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookBackMatterMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookBackMatterMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookBackMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookBackMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\BackMatter $fromNode, CodeBook\BackMatter $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $actual->getIndex() && $actual->getIndex()->setUlid('uuid');
        $actual->getIndex() && $actual->getIndex()->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\BackMatter();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\BackMatter();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setChildren([new BaseBook\Appendix(), new BaseBook\Appendix(), $index = new BaseBook\Index()]);
        $fromNode->setIndex($index);
        $expected->setChildren([new CodeBook\Appendix(), new CodeBook\Appendix(), $index = new CodeBook\Index()]);
        $expected->setIndex($index);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
