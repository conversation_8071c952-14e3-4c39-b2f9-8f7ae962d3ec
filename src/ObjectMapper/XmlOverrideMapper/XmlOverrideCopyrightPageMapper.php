<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideCopyrightPageMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\CopyrightPage $element, CodeBook\CopyrightPage $entity): CodeBook\CopyrightPage
    {
        $this->mapTitleGroup($element, $entity);
        $entity->setBody($element->getBody());

        return $entity;
    }
}
