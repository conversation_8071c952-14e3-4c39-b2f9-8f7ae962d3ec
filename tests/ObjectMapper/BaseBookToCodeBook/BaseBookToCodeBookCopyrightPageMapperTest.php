<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookCopyrightPageMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookCopyrightPageMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookCopyrightPageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookCopyrightPageMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\CopyrightPage $fromNode, CodeBook\CopyrightPage $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\CopyrightPage();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\CopyrightPage();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        $expected->setRole('');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
