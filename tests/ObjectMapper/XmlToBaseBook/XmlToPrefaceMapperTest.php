<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToPrefaceMapper;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\Section;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToPrefaceMapper */
class XmlToPrefaceMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToPrefaceMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToPrefaceMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Preface $element, BaseBook\Preface $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Preface();
        $expected = new BaseBook\Preface();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setBody('<p>Body</p>');
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Section(), new Section()]);
        $expected->setChildren([new BaseBook\Section(), new BaseBook\Section()]);
        yield 'children' => [deep_copy($element), deep_copy($expected)];
    }
}
