<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookTableMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\Table $from): CodeBook\Table
    {
        $to = new CodeBook\Table();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setFloat($from->getFloat());
        $to->setOrientation($from->getOrientation());
        $to->setTocEntry($from->hasTocEntry());
        $to->setFrame($from->getFrame());
        $to->setColumnSeparator($from->isColumnSeparator());
        $to->setRowSeparator($from->isRowSeparator());
        $to->setBackgroundColor($from->getBackgroundColor());
        $to->setTableStyle($from->getTableStyle());
        $to->setPageWide($from->isPageWide());
        $to->setClass($from->getClass());
        $to->setTitleAttr($from->getTitleAttr());
        $to->setSummary($from->getSummary());
        $to->setWidth($from->getWidth());
        $to->setBorder($from->getBorder());
        $to->setCellSpacing($from->getCellSpacing());
        $to->setCellPadding($from->getCellPadding());
        $to->setRules($from->getRules());

        $to->setTable($from->getTable());
        $to->setCaption($from->getCaption());
        $to->setLegend($from->getLegend());
        $to->setSource($from->getSource());
        $to->setCreditTitle($from->getCreditTitle());
        $to->setCredit($from->getCredit());
        $to->setTableNotesTitle($from->getTableNotesTitle());
        $to->setTableNotes($from->getTableNotes());

        return $to;
    }
}
