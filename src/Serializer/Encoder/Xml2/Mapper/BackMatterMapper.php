<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;

/**
 * @since 1.11
 */
class BackMatterMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'backmatter';
    }

    public static function dtoClass(): string
    {
        return BackMatter::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof BackMatter);

        $children = [];
        $validKeys = [
//            'division',
            'appendix',
            'level-1',
//            'glossary',
//            'disposition-list',
//            'endnotes',
//            'bibliography',
            'index',
//            'errata',
        ];

        foreach ($this->mixedContent($reader) as $key => $value) {
            if (!in_array($key, $validKeys)) {
                continue;
            }

            $children[] = $value;
        }

        $element->setChildren($children);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof BackMatter);

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Appendix) {
                $writer->writeElement(self::CLARK . 'appendix', $i);
            } elseif ($i instanceof Index) {
                $writer->writeElement(self::CLARK . 'index', $i);
            } elseif ($i instanceof Level) {
                $i->setDisplayLevel(1);
                $writer->writeElement(self::CLARK . 'level-1', $i);
//            } elseif ($i instanceof BackMatterDivision) {
//                $writer->writeElement(self::CLARK . 'division', $i);
//            } elseif ($i instanceof Glossary) {}
//            } elseif ($i instanceof DispositionList) {}
//            } elseif ($i instanceof EndNotes) {}
//            } elseif ($i instanceof Bibliography) {}
//            } elseif ($i instanceof Errata) {}
            }
        }
    }
}
