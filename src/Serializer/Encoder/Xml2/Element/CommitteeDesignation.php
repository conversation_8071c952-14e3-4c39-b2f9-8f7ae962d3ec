<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class CommitteeDesignation extends AbstractContentElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;

    private string $responsibleFor = '';

    public function getResponsibleFor(): string
    {
        return $this->responsibleFor;
    }

    public function setResponsibleFor(string $responsibleFor): void
    {
        $this->responsibleFor = $responsibleFor;
    }
}
