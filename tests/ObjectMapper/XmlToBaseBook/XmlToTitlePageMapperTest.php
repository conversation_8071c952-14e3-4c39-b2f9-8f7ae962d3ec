<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToTitlePageMapper;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToTitlePageMapper */
class XmlToTitlePageMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToTitlePageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToTitlePageMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(TitlePage $element, BaseBook\TitlePage $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new TitlePage();
        $expected = new BaseBook\TitlePage();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);
    }
}
