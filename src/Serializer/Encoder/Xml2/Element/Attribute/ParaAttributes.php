<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Enum\Align;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @since 1.11
 */
trait ParaAttributes
{
    public bool $indent = false;
    #[Assert\Choice(choices: Align::CASES)]
    public string $align = Align::DEFAULT;

    public function isIndent(): bool
    {
        return $this->indent;
    }

    public function setIndent(bool $indent): void
    {
        $this->indent = $indent;
    }

    public function getAlign(): string
    {
        return $this->align;
    }

    public function setAlign(string $align): void
    {
        $this->align = $align;
    }
}
