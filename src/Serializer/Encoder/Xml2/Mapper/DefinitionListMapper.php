<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

class DefinitionListMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'def-list';
    }

    public static function dtoClass(): string
    {
        return DefinitionList::class;
    }

    /** @param DefinitionList $element */
    protected function readElement(Reader $reader, $element): void
    {
        $children = [];

        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('def-item' === $key && $value instanceof DefinitionItem) {
                $children[] = $value;
            } elseif ('relocated-to' === $key && $value instanceof RelocatedTo) {
                $children[] = $value;
            } elseif ('relocated-from' === $key && $value instanceof RelocatedFrom) {
                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    /** @param DefinitionList $element */
    protected function writeElement(Writer $writer, $element): void
    {
        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof DefinitionItem) {
                $writer->writeElement(self::CLARK . 'def-item', $i);
            } elseif ($i instanceof RelocatedTo) {
                $writer->writeElement(self::CLARK . 'relocated-to', $i);
            } elseif ($i instanceof RelocatedFrom) {
                $writer->writeElement(self::CLARK . 'relocated-from', $i);
            }
        }
    }
}
