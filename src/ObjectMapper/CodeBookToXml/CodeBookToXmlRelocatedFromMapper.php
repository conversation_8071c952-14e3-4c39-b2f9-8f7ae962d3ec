<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlRelocatedFromMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\RelocatedFrom $from): Xml2\RelocatedFrom
    {
        $to = new Xml2\RelocatedFrom();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);

        $to->setAlign($from->getAlign());
        $to->setIndent($from->isIndent());
        $to->setReferenceId($from->getReferenceId());

        $to->setBody($from->getBody());

        return $to;
    }
}
