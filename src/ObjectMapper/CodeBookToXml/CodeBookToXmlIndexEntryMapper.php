<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlIndexEntryMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\IndexEntry $from): Xml2\IndexEntry
    {
        $to = new Xml2\IndexEntry();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);

        $primaryIe = new Xml2\PrimaryIndexEntry();
        $to->setPrimaryIndexEntry($primaryIe);

        if (!empty($from->getReferenceId())) {
            $primaryIe->setReferenceId($from->getReferenceId());
        }

        if (!empty($from->getTerm())) {
            $term = new Xml2\Term();
            $term->setBody($from->getTerm());
            $primaryIe->setTerm($term);
        }

        if (!empty($from->getNavPointerGroup())) {
            $primaryIe->setNavPointerGroup($from->getNavPointerGroup());
        }

        $children = [];
        foreach ($from->getChildren() as $child) {
            $children[] = $this->mapper->map($child);
        }
        $to->setChildren($children);

        return $to;
    }
}
