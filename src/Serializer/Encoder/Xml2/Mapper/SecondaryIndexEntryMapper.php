<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SeeAlsoIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SeeIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function is_string;
use function Sabre\Xml\Deserializer\keyValue;

class SecondaryIndexEntryMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'secondaryie';
    }

    public static function dtoClass(): string
    {
        return SecondaryIndexEntry::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof SecondaryIndexEntry);

        if (array_key_exists('rid', $attrs) && is_string($attrs['rid'])) {
            $element->setReferenceId($attrs['rid']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof SecondaryIndexEntry);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('term' === $key && $value instanceof Term) {
                $element->setTerm($value);
            } elseif ('nav-pointer-group' === $key && is_string($value)) {
                $element->setNavPointerGroup($value);
            } elseif ('seeie' === $key && $value instanceof SeeIndexEntry) {
                throw new \RuntimeException('seeie is not supported');
            } elseif ('seealsoie' === $key && $value instanceof SeeAlsoIndexEntry) {
                throw new \RuntimeException('seealsoie is not supported');
            }
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof SecondaryIndexEntry);

        if (!empty($element->getReferenceId())) {
            $attrs['rid'] = $element->getReferenceId();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof SecondaryIndexEntry);

        if ($element->getTerm()) {
            $writer->writeElement(self::CLARK . 'term', $element->getTerm());
        }

        if (null !== $element->getSeeIndexEntry()) {
            throw new \RuntimeException('seeie is not supported');
        } elseif (null !== $element->getSeeAlsoIndexEntry()) {
            throw new \RuntimeException('seealsoie is not supported');
        }

        if (!empty($element->getNavPointerGroup())) {
            $writer->writeRaw($element->getNavPointerGroup());
        }
    }
}
