<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Enum\Align;
use App\Helper\DateTimeHelper;
use App\Serializer\Encoder\Xml2\Element\AbstractBlockElement;
use App\Serializer\Encoder\Xml2\Element\AbstractContentElement;
use App\Serializer\Encoder\Xml2\Element\OuterXmlInterface;
use App\Serializer\Encoder\Xml2\Xml2Schema;
use DateTime;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function gettype;
use function in_array;
use function is_bool;
use function is_numeric;
use function is_string;
use function preg_replace;
use function property_exists;
use function Sabre\Xml\Deserializer\mixedContent;
use function settype;
use function sprintf;
use function str_replace;
use function trim;

abstract class AbstractMapper implements MapperInterface
{
    const XMLNS = 'https://schema.iccsafe.org/book/schema/1.0';
    const CLARK = '{' . Xml2Schema::XMLNS . '}';

    const REGEX_XMLNS       = '# xmlns(:m|:epub)?="(.*?)"#s';
    const REGEX_TITLE_GROUP = '/<titlegroup\b(?:\s[^>]*)?(?:\/>|>.*?<\/titlegroup>)/ms';

    public static function elementName(): string
    {
        return self::CLARK . static::localName();
    }

    abstract public static function dtoClass(): string;

    public static function clark(string $localName, string $xmlns = Xml2Schema::XMLNS): string
    {
        return sprintf('{%s}%s', $xmlns, $localName);
    }

    public function read(Reader $reader)
    {
        $class = $this->dtoClass();
        $element = new $class();

        if ($element instanceof AbstractBlockElement || $element instanceof OuterXmlInterface) {
            $element->setOuterXml(preg_replace(static::REGEX_XMLNS, '', $reader->readOuterXml()));
        }

        $attrs = $reader->parseAttributes();
        $this->readIdAttribute($element, $attrs);
        $this->readCommonAttributes($element, $attrs);
        $this->readRevisionAttributes($element, $attrs);
        $this->readErrataAttributes($element, $attrs);
        $this->readParaAttributes($element, $attrs);

        $this->readAttributes($element, $attrs);
        $this->readElement($reader, $element);

        return $element;
    }

    public function write(Writer $writer, $element): void
    {
        $attrs = [];

        if ($element->getInternalId() > 0) {
            $attrs['data-internal-id'] = (string) $element->getInternalId();
        }

        $this->writeIdAttribute($element, $attrs);
        $this->writeCommonAttributes($element, $attrs);
        $this->writeRevisionAttributes($element, $attrs);
        $this->writeErrataAttributes($element, $attrs);
        $this->writeParaAttributes($element, $attrs);
        $this->writeAttributes($element, $attrs);

        $writer->writeAttributes($attrs);
        $this->writeElement($writer, $element);
    }

    protected function readAttributes($element, array $attrs): void
    {
    }

    protected function readElement(Reader $reader, $element): void
    {
        if ($element instanceof AbstractContentElement) {
            $innerXml = $reader->readInnerXml();
            $innerXml = preg_replace(self::REGEX_XMLNS, '', $innerXml);
            $element->setBody(trim($innerXml));
        }

        $reader->next();
    }

    protected function writeAttributes($element, array &$attrs): void
    {
    }

    protected function writeElement(Writer $writer, $element): void
    {
        if ($element instanceof AbstractContentElement) {
            $writer->writeRaw($element->getBody());
        }
    }

    protected function writeElements(Writer $writer, iterable $fields): void
    {
        foreach ($fields as $key => $value) {
            if ($value) {
                $writer->writeElement(self::clark($key), $value);
            }
        }
    }

    protected function readStringAttributes($element, array $attrs, array $props): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && array_key_exists($key, $attrs) && is_string($attrs[$key]) && 'string' === gettype($element->{$prop})) {
                $element->{$prop} = $attrs[$key];
            }
        }
    }

    protected function readBooleanAttributes($element, array $attrs, array $props): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && array_key_exists($key, $attrs) && is_string($attrs[$key])) {
                $element->{$prop} = in_array($attrs[$key], ['yes', '1'], true);
            }
        }
    }

    protected function readDecimalAttributes($element, array $attrs, array $props): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && array_key_exists($key, $attrs) && is_numeric($attrs[$key]) && settype($attrs[$key], 'float')) {
                $element->{$prop} = $attrs[$key];
            }
        }
    }

    private function readIdAttribute($element, array $attrs): void
    {
        $this->readStringAttributes($element, $attrs, [
            'id'      => 'id',
            'ctXmlId' => 'ct-xml-id',
        ]);
    }

    private function readCommonAttributes($element, array $attrs): void
    {
        $this->readStringAttributes($element, $attrs, [
            'role'           => 'role',
            'display'        => 'display',
            'language'       => 'lang',
            'additionalInfo' => 'add-info',
        ]);
        $this->readBooleanAttributes($element, $attrs, ['verbatim' => 'verbatim']);
    }

    private function readRevisionAttributes($element, array $attrs): void
    {
        $this->readStringAttributes($element, $attrs, [
            'revisionBy'        => 'revision-by',
            'revision'          => 'revision',
            'revisionGroup'     => 'revision-group',
            'dataChanged'       => 'data-changed',
            'dataChangedIn'     => 'data-changed-in',
            'relocatedFromAttr' => 'relocated-from',
        ]);

        if (property_exists($element, 'revisionDateTime')
            && array_key_exists('revision-datetime', $attrs)
            && is_string($attrs['revision-datetime'])
        ) {
            $element->revisionDateTime = DateTimeHelper::parseToDateTime($attrs['revision-datetime']);
        }
    }

    private function readErrataAttributes($element, array $attrs): void
    {
        $this->readStringAttributes($element, $attrs, [
            'errata'     => 'errata',
            'errataList' => 'errata-list',
        ]);
    }

    private function readParaAttributes($element, array $attrs): void
    {
        $this->readStringAttributes($element, $attrs, ['align' => 'align']);
        $this->readBooleanAttributes($element, $attrs, ['indent' => 'indent']);
    }

    protected function writeStringAttributes($element, array &$attrs, array $props, array $skipDefaults = []): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && is_string($element->{$prop}) && !empty($element->{$prop})) {
                if (array_key_exists($prop, $skipDefaults) && $skipDefaults[$prop] === $element->{$prop}) {
                    continue;
                }
                $attrs[$key] = $element->{$prop};
            }
        }
    }

    protected function writeBooleanAttributes($element, array &$attrs, array $props): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && is_bool($element->{$prop}) && $element->{$prop}) {
                $attrs[$key] = 'yes';
            }
        }
    }

    protected function writeDecimalAttributes($element, array &$attrs, array $props): void
    {
        foreach ($props as $prop => $key) {
            if (property_exists($element, $prop) && is_numeric($element->{$prop}) && $element->{$prop} > 0) {
                $attrs[$key] = (string) $element->{$prop};
            }
        }
    }

    private function writeIdAttribute($element, array &$attrs): void
    {
        $this->writeStringAttributes($element, $attrs, [
            'id'      => 'id',
            'ctXmlId' => 'ct-xml-id',
        ]);
    }

    private function writeCommonAttributes($element, array &$attrs): void
    {
        $this->writeStringAttributes($element, $attrs, [
            'role'           => 'role',
            'display'        => 'display',
            'language'       => 'lang',
            'additionalInfo' => 'add-info',
        ]);
        $this->writeBooleanAttributes($element, $attrs, ['verbatim' => 'verbatim']);
    }

    private function writeRevisionAttributes($element, array &$attrs): void
    {
        $this->writeStringAttributes($element, $attrs, [
            'revisionBy'        => 'revision-by',
            'revision'          => 'revision',
            'revisionGroup'     => 'revision-group',
            'dataChanged'       => 'data-changed',
            'dataChangedIn'     => 'data-changed-in',
            'relocatedFromAttr' => 'relocated-from',
        ]);

        if (property_exists($element, 'revisionDateTime') && $element->revisionDateTime instanceof DateTime) {
            $attrs['revision-datetime'] = DateTimeHelper::parseToString($element->revisionDateTime);
        }
    }

    private function writeErrataAttributes($element, array &$attrs): void
    {
        // errata
        $this->writeStringAttributes($element, $attrs, [
            'errata'     => 'errata',
            'errataList' => 'errata-list',
        ]);
    }

    private function writeParaAttributes($element, array &$attrs): void
    {
        $this->writeStringAttributes($element, $attrs, ['align' => 'align'], ['align' => Align::DEFAULT]);
        $this->writeBooleanAttributes($element, $attrs, ['indent' => 'indent']);
    }

    protected function mixedContent(Reader $reader): iterable
    {
        foreach (mixedContent($reader) as $i) {
            $key = str_replace(self::CLARK, '', $i['name']);
            yield $key => $i['value'];
        }
    }
}
