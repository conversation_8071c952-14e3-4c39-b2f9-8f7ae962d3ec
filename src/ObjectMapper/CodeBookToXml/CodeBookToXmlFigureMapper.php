<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlFigureMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Figure $from): Xml2\Figure
    {
        $to = new Xml2\Figure();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        $to->setTocEntry($from->hasTocEntry());
        $to->setFloat($from->getFloat());
        $to->setOrientation($from->getOrientation());

        $to->setMedia($from->getMedia());
        $to->setLegend($from->getLegend());
        if (!empty($from->getCaption())) {
            $caption = new Xml2\Caption();
            $caption->setBody($from->getCaption());
            $to->setCaption($caption);
        }
        $to->setFigureNotes($this->createFigureNotes($from->getFigureNotesTitle(), $from->getFigureNotes()));
        if (!empty($from->getSource())) {
            $source = new Xml2\Source();
            $source->setBody($from->getSource());
            $to->setSource($source);
        }
        $to->setCredit($this->createCredit($from->getCreditTitle(), $from->getCredit()));

        return $to;
    }

    private function createFigureNotes(string $title, string $body): ?Xml2\FigureNotes
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $figureNotes = new Xml2\FigureNotes();
        $figureNotes->setBody($body);

        if (!empty($title)) {
            $titleEl = new Xml2\Title();
            $titleEl->setBody($title);
            $titleGroup = new Xml2\TitleGroup();
            $titleGroup->setTitle($titleEl);
            $figureNotes->setTitleGroup($titleGroup);
        }

        return $figureNotes;
    }
}
