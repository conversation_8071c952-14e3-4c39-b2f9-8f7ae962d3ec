# Correlation Tool - Visual XML Editor

test pr

Welcome to the Correlation Tool, an advanced visual XML editor that redefines structured content authoring with its
innovative features and intuitive user interface. Our tool makes ICC XML technologies easily accessible to everyone.

## Domains

Correlation Tool is available to be used at the following in-network locations:

CT v1.1 (active)

| Environment   | FE URL                                      | API URL                                |
|---------------|---------------------------------------------|----------------------------------------|
| **Prod**      | https://correlation-beta.iccsafe.org        | https://cct-api-beta.iccsafe.org       |
| **UAT/Stage** | https://correlation-vueui-stage.iccsafe.org | https://cct-api-beta-stage.iccsafe.org |
| **Dev**       | https://correlation-vueui-dev.iccsafe.org   | https://cct-api-beta-dev.iccsafe.org   |

Legacy / REACT

| Environment   | FE URL                                                            | API URL                                                               |
|---------------|-------------------------------------------------------------------|-----------------------------------------------------------------------|
| **Prod**      | https://correlation.iccsafe.org <br/> Never use this for testing! | https://correlation-api.iccsafe.org <br/> Never use this for testing! |
| **UAT/Stage** | https://correlation-stage.iccsafe.org                             | https://correlation-api-stage.iccsafe.org/                            |

- **Prod**: https://correlation-beta.iccsafe.org - Never use this for testing!
- **Stage**: https://correlation-vueui-stage.iccsafe.org
- **Dev**: https://correlation-vueui-dev.iccsafe.org

## Requirements

Before you begin, ensure you have the following installed on your system:

- **PHP 7.4**: Yes, for now.
- **mySQL 8 / MariaDB**: Database platform. MariaDB is preferred lately.
- **Rancher Desktop**: This is required for managing Docker containers. Download it
  from [Rancher Desktop](https://rancherdesktop.io/).
- **Symfony executable**: Download it from [symfony.com](https://symfony.com/download).

Or is you are sane:

```console
# Install brew if needed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install all the things
brew tap shivammathur/php
brew install shivammathur/php/php@7.4 mariadb rancher symfony-cli
```

## Installation

Follow these steps to get the Correlation Tool up and running on your local machine:

### Step 1: Clone the Repository

First, clone the repository to your local machine using the following commands:

```bash
<NAME_EMAIL>:InternationalCodeCouncil/correlation-tool.git
cd correlation-tool
```

### Step 1.5: Local config

Create a `.env.local` file in the project root. Only a handful of settings are needed in it:

```bash
# Environment. Use "dev" only here.
APP_ENV=dev

# Database connection string. Follow Symfony guidelines if you want something different. Example here uses local mariaDB with database "ct".
DATABASE_URL="mysql://root:root@127.0.0.1:3306/ct?serverVersion=mariadb-11.5.2"

# Optional: URL of your local API (for absolute paths of images, emails, etc.)
CT_API_URL=https://api.ct.test
```

Always make your local changes in this file.

Now lets add our secrets. They are stored encrypted in this repository. Get the decryption key from another dev and run
this, and add it to your ~/.profile file to make your life easier.

```console
export SYMFONY_DECRYPTION_SECRET="<key you got from another dev>"
```

Running this command will create a new `.env.dev.local` file with the decrypted secrets:

```console
bin/console secrets:decrypt-to-local --force
```

Now that the dirty work is out of the way, you can see how your environment variables and these files build the config
stage by running:

`bin/console debug:dotenv`

THe output will look similar to this table, with the values getting overwritten from the right-most file to the left.

| Variable          | Value                      | .env.dev.local | .env.dev   | .env.local  | .env         |
|-------------------|----------------------------|----------------|------------|-------------|--------------|
| APP_ENV           | dev                        | n/a            | n/a        | dev         | prod         |
| APP_SECRET        | agsviohkiaowpet[wugjkdasdf | n/a            | n/a        | n/a         | sadklgfdj... |
| APP_VERSION       | api.ct.test                | n/a            | n/a        | api.ct.test | dev          |
| ATHENA_BASEX_HOST | **********                 | n/a            | ********** | n/a         | **********   |

### Step 2: Start the API Application via Docker

Next, use Docker to build and start the application. Run the following command:

```bash
docker compose up -d --build
```

## Usage

Once the application is running, you can interact with it as follows:

- **API Documentation**: Access the API documentation at `http://127.0.0.1:8201/api/doc` to explore the available
  endpoints and their usage.
- **API Endpoints**: You can use API clients like Postman or cURL to access the API endpoints and perform various
  operations.

## Contributing

1. **Create a Branch**: Create a new branch for your feature or bug fix.
2. **Submit a Pull Request**: Open a pull request with a clear description of your changes.

For any questions or issues, please bring them up with the team.

## License

This project is proprietary and is © International Code Council. All rights reserved.

## Test Users

These are the dedicated test users. These users accept any non-blank password*

| User                | Email Address                    |
|---------------------|----------------------------------|
| Super Admin         | <EMAIL>  |
| Admin               | <EMAIL>         |
| Codes               | <EMAIL>         |
| Pubs                | <EMAIL>          |
| Secretariate        | <EMAIL>         |
| Viewer              | <EMAIL>          |
| Always Bad Password | <EMAIL> |

\* <EMAIL> will always reject the password. Used for testing that function.

## Data-flow diagram and explanation of how the XML2 system works in CCT

                            ┌────────────────────┐
                            │ Doctrine Entities  │
                            │ (e.g., Chapter,    │  ← stored in DB
                            │  Section, Figure)  │
                            └────────┬───────────┘
                                     │
                                     │ normalize (map to XML2 Elements)
                                     ▼
                            ┌──────────────────────────┐
                            │ Xml2 Element Objects     │
                            │ (e.g., Xml2\Section,     │
                            │  Xml2\Chapter, etc.)     │
                            └────────┬─────────────────┘
                                     │
                                     │ encode (XML2Encoder)
                                     ▼
                            ┌──────────────────────────┐
                            │ XML Document             │
                            │ (compliant with ICC XSD) │
                            │ <level role="chapter">…  │
                            │ <section>…</section>     │
                            └────────┬─────────────────┘
                                     │
                                     │ transform (XSLT / HTML view )
                                     ▼
                            ┌──────────────────────────┐
                            │ Final Output             │
                            │ - Rendered XML           │
                            │ - HTML preview           │
                            │ - Redline diff, etc.     │
                            └──────────────────────────┘
