<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SeeIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function is_string;
use function Sabre\Xml\Deserializer\keyValue;

class SeeIndexEntryMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'seeie';
    }

    public static function dtoClass(): string
    {
        return SeeIndexEntry::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof SeeIndexEntry);

        if (array_key_exists('prefix', $attrs) && is_string($attrs['prefix'])) {
            $element->setPrefix($attrs['prefix']);
        }
        if (array_key_exists('rid', $attrs) && is_string($attrs['rid'])) {
            $element->setReferenceId($attrs['rid']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof SeeIndexEntry);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('term' === $key && $value instanceof Term) {
                $element->setTerm($value);
            } elseif ('nav-pointer-group' === $key && is_string($value)) {
                $element->setNavPointerGroup($value);
            }
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof SeeIndexEntry);

        // <xs:attribute name="prefix" type="xs:string" default="See"/>
        if (!empty($element->getPrefix()) && 'See' !== $element->getPrefix()) {
            $attrs['prefix'] = $element->getPrefix();
        }
        if (!empty($element->getReferenceId())) {
            $attrs['rid'] = $element->getReferenceId();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof SeeIndexEntry);

        if ($element->getTerm()) {
            $writer->writeElement(self::CLARK . 'term', $element->getTerm());
        }
        if (!empty($element->getNavPointerGroup())) {
            $writer->writeRaw($element->getNavPointerGroup());
        }
    }
}
