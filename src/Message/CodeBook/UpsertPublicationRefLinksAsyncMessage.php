<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Message\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;

class UpsertPublicationRefLinksAsyncMessage
{
    private int $nodeId;

    public function __construct(AbstractCodeBookNode $codeBookNode)
    {
        $this->nodeId = $codeBookNode->getId();
    }

    public function getNodeId(): int
    {
        return $this->nodeId;
    }
}
