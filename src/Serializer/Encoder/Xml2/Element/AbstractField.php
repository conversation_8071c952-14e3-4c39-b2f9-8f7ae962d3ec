<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class AbstractField extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;

    use Field\TitleGroupTrait;

    public string $body = '';

    /** @deprecated */
    public function __construct(string $body = '')
    {
        $this->body = $body;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): void
    {
        $this->body = $body;
    }
}
