<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFigureMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlFigureMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlFigureMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlFigureMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Figure $from, Xml2\Figure $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Figure();
        $from->setNodeId('');
        $expected = new Xml2\Figure();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setFloat(FloatEnum::TOP);
        $expected->setFloat(FloatEnum::TOP);
        yield 'float' => [deep_copy($from), deep_copy($expected)];

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'orientation' => [deep_copy($from), deep_copy($expected)];

        $from->setMedia('media');
        $expected->setMedia('media');
        yield 'media' => [deep_copy($from), deep_copy($expected)];

        $from->setCaption('caption');
        $caption = new Xml2\Caption();
        $caption->setBody('caption');
        $expected->setCaption($caption);
        yield 'caption' => [deep_copy($from), deep_copy($expected)];

        $from->setFigureNotes('figureNotes');
        $figureNotes = new Xml2\FigureNotes();
        $figureNotes->setBody('figureNotes');
        $expected->setFigureNotes($figureNotes);
        yield 'figureNotes' => [deep_copy($from), deep_copy($expected)];

        $from->setFigureNotesTitle('figureNotesTitle');
        $title = new Xml2\Title();
        $title->setBody('figureNotesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $figureNotes->setTitleGroup($titleGroup);
        yield 'figureNotesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setLegend('legend');
        $expected->setLegend('legend');
        yield 'legend' => [deep_copy($from), deep_copy($expected)];

        $from->setSource('source');
        $source = new Xml2\Source();
        $source->setBody('source');
        $expected->setSource($source);
        yield 'source' => [deep_copy($from), deep_copy($expected)];

        $from->setCredit('credit');
        $credit = new Xml2\Credit();
        $credit->setBody('credit');
        $expected->setCredit($credit);
        yield 'credit' => [deep_copy($from), deep_copy($expected)];

        $from->setCreditTitle('creditTitle');
        $title = new Xml2\Title();
        $title->setBody('creditTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $credit->setTitleGroup($titleGroup);
        yield 'creditTitle' => [deep_copy($from), deep_copy($expected)];
    }
}
