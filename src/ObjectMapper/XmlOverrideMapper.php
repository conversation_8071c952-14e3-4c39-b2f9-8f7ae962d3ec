<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Exception\ApiException;
use Doctrine\ORM\EntityManagerInterface;
use App\ObjectMapper\XmlOverrideMapper\{
    XmlOverrideAppendixMapper,
    XmlOverrideChapterMapper,
    XmlOverrideCopyrightPageMapper,
    XmlOverrideDefinitionListMapper,
    XmlOverrideDefinitionMapper,
    XmlOverrideFigureMapper,
    XmlOverrideForewordMapper,
    XmlOverrideIndexDivisionMapper,
    XmlOverrideIndexEntryMapper,
    XmlOverrideIndexMapper,
    XmlOverridePrefaceMapper,
    XmlOverridePromulgatorMapper,
    XmlOverridePublisherNoteMapper,
    XmlOverrideReferenceStandardMapper,
    XmlOverrideRelocatedFromMapper,
    XmlOverrideRelocatedToMapper,
    XmlOverrideSectionMapper,
    XmlOverrideTableMapper,
    XmlOverrideTitlePageMapper
};
use App\Serializer\Encoder\Xml2\Element\{AbstractElement,
    Appendix,
    CopyrightPage,
    DefinitionItem,
    DefinitionList,
    Figure,
    Foreword,
    IndexDivision,
    IndexEntry,
    Index,
    Level,
    Preface,
    Promulgator,
    PublisherNote,
    Reference,
    RelocatedFrom,
    RelocatedTo,
    Section,
    Table,
    TitlePage
};

use function get_class;
use function sprintf;

class XmlOverrideMapper
{
    public function __construct(
        private readonly XmlOverrideAppendixMapper          $appendixMapper,
        private readonly XmlOverrideChapterMapper           $chapterMapper,
        private readonly XmlOverrideCopyrightPageMapper     $copyrightPageMapper,
        private readonly XmlOverrideDefinitionListMapper    $definitionListMapper,
        private readonly XmlOverrideDefinitionMapper        $definitionMapper,
        private readonly XmlOverrideFigureMapper            $figureMapper,
        private readonly XmlOverrideForewordMapper          $forewordMapper,
        private readonly XmlOverrideIndexDivisionMapper     $indexDivisionMapper,
        private readonly XmlOverrideIndexEntryMapper        $indexEntryMapper,
        private readonly XmlOverrideIndexMapper             $indexMapper,
        private readonly XmlOverridePrefaceMapper           $prefaceMapper,
        private readonly XmlOverridePromulgatorMapper       $promulgatorMapper,
        private readonly XmlOverridePublisherNoteMapper     $publisherNoteMapper,
        private readonly XmlOverrideReferenceStandardMapper $referenceStandardMapper,
        private readonly XmlOverrideRelocatedFromMapper     $relocatedFromMapper,
        private readonly XmlOverrideRelocatedToMapper       $relocatedToMapper,
        private readonly XmlOverrideSectionMapper           $sectionMapper,
        private readonly XmlOverrideTableMapper             $tableMapper,
        private readonly XmlOverrideTitlePageMapper         $titlePageMapper,
        private readonly EntityManagerInterface             $entityManager
    ) {
    }

    /** @throws ApiException */
    public function map(AbstractElement $from): AbstractCodeBookNode
    {
        $object = $this->entityManager->getRepository(AbstractCodeBookNode::class)->findOneBy(['nodeId' => $from->getId()]);

        assert($object instanceof AbstractCodeBookNode);

        $node = match (true) {
            $from instanceof Appendix       => $this->appendixMapper->map($from, $object),
            $from instanceof Level          => $this->chapterMapper->map($from, $object),
            $from instanceof CopyrightPage  => $this->copyrightPageMapper->map($from, $object),
            $from instanceof DefinitionList => $this->definitionListMapper->map($from, $object),
            $from instanceof DefinitionItem => $this->definitionMapper->map($from, $object),
            $from instanceof Figure         => $this->figureMapper->map($from, $object),
            $from instanceof Foreword       => $this->forewordMapper->map($from, $object),
            $from instanceof IndexDivision  => $this->indexDivisionMapper->map($from, $object),
            $from instanceof IndexEntry     => $this->indexEntryMapper->map($from, $object),
            $from instanceof Index          => $this->indexMapper->map($from, $object),
            $from instanceof Preface        => $this->prefaceMapper->map($from, $object),
            $from instanceof Promulgator    => $this->promulgatorMapper->map($from, $object),
            $from instanceof PublisherNote  => $this->publisherNoteMapper->map($from, $object),
            $from instanceof Reference      => $this->referenceStandardMapper->map($from, $object),
            $from instanceof RelocatedFrom  => $this->relocatedFromMapper->map($from, $object),
            $from instanceof RelocatedTo    => $this->relocatedToMapper->map($from, $object),
            $from instanceof Section        => $this->sectionMapper->map($from, $object),
            $from instanceof Table          => $this->tableMapper->map($from, $object),
            $from instanceof TitlePage      => $this->titlePageMapper->map($from, $object),
            default                         => null,
        };

        if (null === $node) {
            throw new ApiException(sprintf('%s is not a supported format', get_class($from)));
        }

        return $node;
    }
}
