<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookReferenceStandardMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookReferenceStandardMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookReferenceStandardMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookReferenceStandardMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\ReferenceStandard $fromNode, CodeBook\ReferenceStandard $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\ReferenceStandard();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\ReferenceStandard();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setNumber('number');
        $expected->setNumber('number');
        yield 'refStdNumber' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitle('title');
        $expected->setTitle('title');
        yield 'refStdTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setNavPointerGroup('navPointerGroup');
        $expected->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
