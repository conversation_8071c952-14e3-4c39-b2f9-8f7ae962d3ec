<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\SectionMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SectionMapper
 */
class SectionMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Section::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Section::class]);
        $this->assertInstanceOf(SectionMapper::class, $service->classMap[Section::class][0]);

        $this->assertArrayHasKey(SectionMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SectionMapper::elementName()]);
        $this->assertInstanceOf(SectionMapper::class, $service->elementMap[SectionMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Section $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<section %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Section();
        $element->setRole('section');
        $attrs = [
            'disp-level' => '1',
        ];
        yield 'baseline' => [deep_copy($element), $attrs];

        // commented out due to forced 'role'
//        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setIndexNumber('index number');
        $attrs['indexnum'] = 'index number';
        yield 'indexnum' => [deep_copy($element), $attrs];

        $element->setDisplayLevel(3);
        $attrs['disp-level'] = '3';
        yield 'disp-level' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];

        $element->setReserveCount(10);
        $attrs['reservecount'] = '10';
        yield 'reservecount' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Section $element, string $innerXml): void
    {
        $xml = sprintf('<section disp-level="1" xmlns="%s" xmlns:m="%s">%s</section>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Section();
        $element->setRole('section');
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setAbstract(new AbstractField());
        $innerXml .= '<abstract/>';
        yield 'abstract' => [deep_copy($element), $innerXml];

        $element->setKeywords(new Keywords());
        $innerXml .= '<keywords/>';
        yield 'keywords' => [deep_copy($element), $innerXml];

        $element->setBody(new SectionBody());
        $innerXml .= '<section-body/>';
        yield 'body' => [deep_copy($element), $innerXml];

        $child = new Section();
        $child->setRole('section');
        $element->setChildren([$child]);
        $innerXml .= '<section disp-level="1"/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $element->setChildren([$child, $child]);
        $innerXml .= '<section disp-level="1"/>';
        yield '2 children' => [deep_copy($element), $innerXml];
    }
}
