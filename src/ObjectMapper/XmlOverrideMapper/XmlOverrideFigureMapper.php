<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideFigureMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Figure $element, CodeBook\Figure $entity): CodeBook\Figure
    {
        $this->mapTitleGroup($element, $entity);
        $entity->setOrientation($element->getOrientation());

        $entity->setMedia($element->getMedia());

        if ($caption = $element->getCaption()) {
            $entity->setCaption($caption->getBody());
        }

        if ($figureNotes = $element->getFigureNotes()) {
            $entity->setFigureNotes($figureNotes->getBody());

            if ($figureNotes->getTitleGroup() && $title = $figureNotes->getTitleGroup()->getTitle()) {
                $entity->setFigureNotesTitle($title->getBody());
            }
        }

        $entity->setLegend($element->getLegend());

        if ($source = $element->getSource()) {
            $entity->setSource($source->getBody());
        }

        if ($credit = $element->getCredit()) {
            $entity->setCredit($credit->getBody());

            if ($credit->getTitleGroup() && $title = $credit->getTitleGroup()->getTitle()) {
                $entity->setCreditTitle($title->getBody());
            }
        }

        return $entity;
    }
}
