<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use App\Serializer\Encoder\Xml2\Mapper\FrontMatterMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\FrontMatterMapper
 */
class FrontMatterMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(FrontMatter::class, $service->classMap);
        $this->assertIsCallable($service->classMap[FrontMatter::class]);
        $this->assertInstanceOf(FrontMatterMapper::class, $service->classMap[FrontMatter::class][0]);

        $this->assertArrayHasKey(FrontMatterMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[FrontMatterMapper::elementName()]);
        $this->assertInstanceOf(FrontMatterMapper::class, $service->elementMap[FrontMatterMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(FrontMatter $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<frontmatter %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new FrontMatter();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(FrontMatter $element, string $innerXml): void
    {
        $xml = sprintf('<frontmatter xmlns="%s" xmlns:m="%s">%s</frontmatter>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new FrontMatter();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $tg = new TitleGroup();
        $tg->setTitle(new Title());

        $titlePage = new TitlePage();
        $titlePage->setId('titlePage');
        $titlePage->setTitleGroup($tg);
        $element->setTitlePage($titlePage);
        $innerXml .= '<titlepage id="titlePage"><titlegroup><title/></titlegroup></titlepage>';
        yield 'titlepage' => [deep_copy($element), $innerXml];

        $copyright = new CopyrightPage();
        $copyright->setId('copyrightPage');
        $element->setCopyrightPage($copyright);
        $innerXml .= '<copyright-page id="copyrightPage"/>';
        yield 'copyright-page' => [deep_copy($element), $innerXml];

        $publisherNote = new PublisherNote();
        $publisherNote->setId('publisherNote');
        $element->setPublisherNote($publisherNote);
        $innerXml .= '<publisher-note id="publisherNote"/>';
        yield 'publisher-note' => [deep_copy($element), $innerXml];

        $foreword = new Foreword();
        $foreword->setId('foreword');
        $element->setForeword($foreword);
        $innerXml .= '<foreword id="foreword"/>';
        yield 'foreword' => [deep_copy($element), $innerXml];

        $preface = new Preface();
        $preface->setId('preface');
        $element->setPreface($preface);
        $innerXml .= '<preface id="preface"/>';
        yield 'preface' => [deep_copy($element), $innerXml];

        $child1 = new Section();
        $child1->setId('child1');
        $child1->setRole('section');
        $element->setChildren([$child1]);
        $innerXml .= '<section id="child1" disp-level="1"/>';
        yield 'child 1' => [deep_copy($element), $innerXml];

        $child2 = new Section();
        $child2->setId('child2');
        $child2->setRole('section');
        $element->setChildren([$child1, $child2]);
        $innerXml .= '<section id="child2" disp-level="1"/>';
        yield 'child 2' => [deep_copy($element), $innerXml];
    }
}
