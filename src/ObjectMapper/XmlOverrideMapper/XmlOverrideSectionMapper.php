<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Serializer\Encoder\Xml2\Element;
use Doctrine\ORM\EntityManagerInterface;

class XmlOverrideSectionMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Level|Element\Section $element, CodeBook\Section $entity): CodeBook\Section
    {
        $this->mapTitleGroup($element, $entity);

        if ($body = $element->getBody()) {
            $entity->setBody($body->getBody());
        }

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
