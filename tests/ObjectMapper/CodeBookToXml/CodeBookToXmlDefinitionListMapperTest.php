<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionListMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlDefinitionListMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlDefinitionListMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\DefinitionList $from, Xml2\DefinitionList $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\DefinitionList();
        $from->setNodeId('');
        $expected = new Xml2\DefinitionList();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $childDefinition = new CodeBook\Definition();
        $childDefinition->setNodeId('childNode');
        $from->addChild($childDefinition);

        $expectedChild = new Xml2\DefinitionItem();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
