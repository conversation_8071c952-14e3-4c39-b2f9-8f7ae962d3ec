<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\Volume;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class VolumeMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'volume';
    }

    public static function dtoClass(): string
    {
        return Volume::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Volume);

        if (array_key_exists('customer-id', $attrs) && is_string($attrs['customer-id'])) {
            $element->setCustomerId($attrs['customer-id']);
        }
        if (array_key_exists('title-type', $attrs) && is_string($attrs['title-type'])) {
            $element->setTitleType($attrs['title-type']);
        }
        if (array_key_exists('governing-type', $attrs) && is_string($attrs['governing-type'])) {
            $element->setGoverningType($attrs['governing-type']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Volume);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('metadata' === $key && $value instanceof Metadata) {
                $element->setMetadata($value);
            } elseif ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('frontmatter' === $key && $value instanceof FrontMatter) {
                $element->setFrontMatter($value);
            } elseif ('backmatter' === $key && $value instanceof BackMatter) {
                $element->setBackMatter($value);
            } elseif ('level-1' === $key && $value instanceof Level) {
                $children[] = $value;
//            } elseif ('division' === $key) {
//                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Volume);

        if (!empty($element->getCustomerId())) {
            $attrs['customer-id'] = $element->getCustomerId();
        }
        if (!empty($element->getTitleType())) {
            $attrs['title-type'] = $element->getTitleType();
        }
        if (!empty($element->getGoverningType())) {
            $attrs['governing-type'] = $element->getGoverningType();
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Volume);

        if ($element->getMetadata()) {
            $writer->writeElement(self::CLARK . 'metadata', $element->getMetadata());
        }
        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if ($element->getFrontMatter()) {
            $writer->writeElement(self::CLARK . 'frontmatter', $element->getFrontMatter());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Level) {
                $i->setDisplayLevel(1);
                $writer->writeElement(self::CLARK . 'level-1', $i);
//            } elseif ($i instanceof Division) {
//                $writer->writeElement(self::CLARK . 'division', $i);
            }
        }

        if ($element->getBackMatter()) {
            $writer->writeElement(self::CLARK . 'backmatter', $element->getBackMatter());
        }
    }
}
