<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use Symfony\Component\PropertyAccess\PropertyAccessor;

use function call_user_func;
use function is_string;

trait ParseFieldsTrait
{
    protected function parseFields(AbstractCodeBookNode $codeBookNode, callable $method, $thirdParam = null): void
    {
        $pa = new PropertyAccessor();

        $fields = [
            'committeeDesignation',
            'superTitle',
            'label',
            'number',
            'correlated',
            'title',
            'subTitle',
            'body',
            'objectives',
            'objectivesTitle',
            'history',
            'note',
            'noteTitle',
            'abstract',
            'abstractTitle',
            'keywords',
            'keywordsTitle',
            'term',
            'definition',
            'caption',
            'legend',
            'source',
            'credit',
            'creditTitle',
            'figureNotes',
            'figureNotesTitle',
            'navPointerGroup',
            'acronym',
            'table',
            'tableNotes',
            'tableNotesTitle',
        ];

        foreach ($fields as $field) {
            try {
                if ($pa->isReadable($codeBookNode, $field) && $pa->isWritable($codeBookNode, $field)) {
                    $oldValue = $pa->getValue($codeBookNode, $field);
                    if (is_string($oldValue) && !empty($oldValue)) {
                        $newValue = call_user_func($method, $oldValue, $codeBookNode, $thirdParam);
                        if ($oldValue !== $newValue) {
                            $pa->setValue($codeBookNode, $field, $newValue);
                        }
                    }
                }
            } catch (\Exception $e) {}
        }
    }
}
