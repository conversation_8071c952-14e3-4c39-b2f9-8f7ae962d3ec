<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlCopyrightPageMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\CopyrightPage $from): Xml2\CopyrightPage
    {
        $to = new Xml2\CopyrightPage();
        $this->mapCommonAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        $to->setBody($from->getBody());

        return $to;
    }
}
