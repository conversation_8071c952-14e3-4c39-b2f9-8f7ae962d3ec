<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Field;

use App\Serializer\Encoder\Xml2\Element\TitleGroup;

trait TitleGroupTrait
{
    public ?TitleGroup $titleGroup = null;

    public function getTitleGroup(): ?TitleGroup
    {
        return $this->titleGroup;
    }

    public function setTitleGroup(?TitleGroup $titleGroup): void
    {
        $this->titleGroup = $titleGroup;
    }
}
