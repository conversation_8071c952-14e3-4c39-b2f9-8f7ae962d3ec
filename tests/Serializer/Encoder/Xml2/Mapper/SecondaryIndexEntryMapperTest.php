<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\SecondaryIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SecondaryIndexEntryMapper
 */
class SecondaryIndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SecondaryIndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SecondaryIndexEntry::class]);
        $this->assertInstanceOf(SecondaryIndexEntryMapper::class, $service->classMap[SecondaryIndexEntry::class][0]);

        $this->assertArrayHasKey(SecondaryIndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SecondaryIndexEntryMapper::elementName()]);
        $this->assertInstanceOf(SecondaryIndexEntryMapper::class, $service->elementMap[SecondaryIndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(SecondaryIndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<secondaryie %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new SecondaryIndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(SecondaryIndexEntry $element, string $innerXml): void
    {
        $xml = sprintf('<secondaryie xmlns="%s" xmlns:m="%s">%s</secondaryie>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new SecondaryIndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTerm(new Term());
        $innerXml .= '<term/>';
        yield 'term' => [deep_copy($element), $innerXml];

//        $element->setSeeIndexEntry(new SeeIndexEntry());
//        $innerXml .= '<seeie/>';
//        yield 'seeie' => [deep_copy($element), $innerXml];

//        $element->setSeeAlsoIndexEntry(new SeeAlsoIndexEntry());
//        $innerXml .= '<seealsoie/>';
//        yield 'seealsoie' => [deep_copy($element), $innerXml];

        $element->setNavPointerGroup('<nav-pointer-group/>');
        $innerXml .= '<nav-pointer-group/>';
        yield 'nav-pointer-group' => [deep_copy($element), $innerXml];
    }
}
