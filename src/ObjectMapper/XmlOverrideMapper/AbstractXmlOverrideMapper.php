<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\ObjectMapper\XmlOverrideMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\Title;
use Symfony\Contracts\Service\Attribute\Required;

use function call_user_func;
use function method_exists;

abstract class AbstractXmlOverrideMapper
{
    protected ?XmlOverrideMapper $mapper;

    #[Required]
    public function setXmlToCodeBookMapper(XmlOverrideMapper $mapper): void
    {
        $this->mapper = $mapper;
    }

    protected function mapTitleGroup(AbstractElement $from, AbstractCodeBookNode $to): void
    {
        if (!method_exists($from, 'getTitleGroup')) {
            return;
        }

        $titleGroup = $from->getTitleGroup();
        if (null === $titleGroup) {
            return;
        }

        $mapping = [
            'getSuperTitle'           => 'setSuperTitle',
            'getCommitteeDesignation' => 'setCommitteeDesignation',
            'getLabel'                => 'setLabel',
            'getNumber'               => 'setNumber',
            'getCorrelated'           => 'setCorrelated',
            'getTitle'                => 'setTitle',
            'getSubTitle'             => 'setSubTitle',
            'getHistory'              => 'setTitleHistory',
        ];
        foreach ($mapping as $getter => $setter) {
            $el = call_user_func([$titleGroup, $getter]);
            if ($el && method_exists($to, $setter)) {
                call_user_func([$to, $setter], $el->getBody());

                if ($el instanceof Title) {
                    /** @var Title $to */
                    $to->setTitleAbbreviation($el->getTitleAbbreviation());
                    $to->setTitleYear($el->getTitleYear());
                }
            }
        }
    }
}
