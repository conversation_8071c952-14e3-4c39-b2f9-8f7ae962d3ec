<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\DataFixtures;

use App\DataFixtures\Project\ProjectFixture;
use App\Event\CodeBook\CodeBookNodePostUpdateEvent;
use App\EventListener\CodeBook\UpsertLinksListener;
use App\Message\CodeBook\UpsertUrlLinksAsyncMessage;
use App\Message\CodeBook\UpsertXRefLinksAsyncMessage;
use App\Message\Project\Action\UpdateActionWorkflowStatusMessage;
use App\Message\Project\CodeChanges\AssociateCodeChangesMessage;
use App\MessageHandler\CodeBook\UpsertUrlLinksAsyncMessageHandler;
use App\MessageHandler\CodeBook\UpsertXRefLinksAsyncMessageHandler;
use App\MessageHandler\Project\Action\UpdateActionWorkflowStatusMessageHandler;
use App\MessageHandler\Project\CodeChanges\AssociateCodeChangesMessageHandler;
use App\Service\StopwatchService;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Messenger\Transport\TransportInterface;
use Symfony\Component\Stopwatch\StopwatchEvent;

use function count;

class AsyncBusFixture extends Fixture implements DependentFixtureInterface, FixtureGroupInterface
{
    private StopwatchEvent $stopwatch;

    public function __construct(
        private readonly TransportInterface                      $asyncTransport,
        private readonly UpsertUrlLinksAsyncMessageHandler       $upsertUrlLinksMessageHandler,
        private readonly UpsertXRefLinksAsyncMessageHandler      $upsertXRefLinksMessageHandler,
        private readonly AssociateCodeChangesMessageHandler     $associateCodeChangesMessageHandler,
        private readonly UpdateActionWorkflowStatusMessageHandler $updateActionWorkflowStatusMessageHandler,
        private readonly EventDispatcherInterface                $eventDispatcher,
        private readonly UpsertLinksListener                     $upsertLinksListener,
        StopwatchService                                         $stopwatch,
    ) {
        $this->stopwatch = $stopwatch->create('AsyncBusFixture');
    }

    public function load(ObjectManager $manager): void
    {
        $this->stopwatch->start();
        $count = 0;

        // Disable auto-flushing in handlers for batch processing
        $this->upsertXRefLinksMessageHandler->setAutoFlush(false);
        $this->upsertUrlLinksMessageHandler->setAutoFlush(false);

        // Disable UpsertLinksListener to prevent recursive message generation
        // When handlers update nodes, postUpdate events would trigger new messages
        $this->eventDispatcher->removeListener(
            CodeBookNodePostUpdateEvent::class,
            [$this->upsertLinksListener, 'queue']
        );

        $totalMessages = count($this->asyncTransport->get());
        if ($totalMessages > 0) {
            echo "\n> processing $totalMessages async messages\n";
        }

        do {
            $messages = $this->asyncTransport->get();

            foreach ($messages as $envelope) {
                $message = $envelope->getMessage();

                $handler = match (true) {
                    $message instanceof UpsertUrlLinksAsyncMessage       => $this->upsertUrlLinksMessageHandler,
                    $message instanceof UpsertXRefLinksAsyncMessage      => $this->upsertXRefLinksMessageHandler,
                    $message instanceof AssociateCodeChangesMessage      => $this->associateCodeChangesMessageHandler,
                    $message instanceof UpdateActionWorkflowStatusMessage => $this->updateActionWorkflowStatusMessageHandler,
                    default                                              => null,
                };

                if ($handler) {
                    $this->asyncTransport->ack($envelope);
                    $handler($message);

                    $count++;
                    // Flush every 500 messages for better memory management
                    if (0 === $count % 500) {
                        $manager->flush();
                        echo "  ✓ Processed $count messages\n";
                    }
                } else {
                    $this->asyncTransport->reject($envelope);
                }
            }
        } while (count($this->asyncTransport->get()) > 0);

        $manager->flush();
        echo "  ✓ Final flush - Total processed: $count messages\n";
        $this->stopwatch->stop();
    }

    public function getDependencies(): array
    {
        return [ProjectFixture::class];
    }

    public static function getGroups(): array
    {
        return ['test'];
    }
}
