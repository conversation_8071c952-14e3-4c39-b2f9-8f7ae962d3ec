<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

/**
 * @since 1.11
 */
trait PageWide
{
    public bool $pageWide = false;

    public function isPageWide(): bool
    {
        return $this->pageWide;
    }

    public function setPageWide(bool $pageWide): void
    {
        $this->pageWide = $pageWide;
    }
}
