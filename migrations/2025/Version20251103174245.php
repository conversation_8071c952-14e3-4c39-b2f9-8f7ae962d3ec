<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251103174245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE code_book_link_publication_ref (id INT AUTO_INCREMENT NOT NULL, code_book_id INT NOT NULL, from_node_id INT NOT NULL, uuid VARCHAR(36) NOT NULL, publication_id LONGTEXT NOT NULL, internal_id LONGTEXT NOT NULL, role LONGTEXT NOT NULL, body LONGTEXT NOT NULL, original_body LONGTEXT NOT NULL, valid_publication_id TINYINT(1) DEFAULT 1 NOT NULL, custom_body TINYINT(1) DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_D92F79ABD17F50A6 (uuid), INDEX IDX_D92F79AB2E05F71D (code_book_id), INDEX IDX_D92F79ABC0537C78 (from_node_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE code_book_link_publication_ref ADD CONSTRAINT FK_D92F79AB2E05F71D FOREIGN KEY (code_book_id) REFERENCES code_book_publication (id)');
        $this->addSql('ALTER TABLE code_book_link_publication_ref ADD CONSTRAINT FK_D92F79ABC0537C78 FOREIGN KEY (from_node_id) REFERENCES code_book_nodes (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE code_book_link_publication_ref DROP FOREIGN KEY FK_D92F79AB2E05F71D');
        $this->addSql('ALTER TABLE code_book_link_publication_ref DROP FOREIGN KEY FK_D92F79ABC0537C78');
        $this->addSql('DROP TABLE code_book_link_publication_ref');
    }
}
