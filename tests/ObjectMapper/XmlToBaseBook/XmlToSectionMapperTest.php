<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToSectionMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToSectionMapper */
class XmlToSectionMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToSectionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToSectionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Section $element, BaseBook\Section $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Section();
        $expected = new BaseBook\Section();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setReserveCount('1');
        $expected->setReserveCount(1);
        yield '@reserve-count' => [deep_copy($element), deep_copy($expected)];

        $element->setDisplayLevel('1');
        $expected->setDisplayLevel(1);
        yield '@display-level' => [deep_copy($element), deep_copy($expected)];

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        $keywords = new Keywords();
        $keywords->setBody('<keyword>Keyword</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Keyword</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Section(), new Section()]);
        $expected->setChildren([new BaseBook\Section(), new BaseBook\Section()]);
        yield 'children' => [deep_copy($element), deep_copy($expected)];
    }
}
