<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideAppendixMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideAppendixMapper */
class XmlOverrideAppendixMapperTest extends KernelTestCase
{
    private ?XmlOverrideAppendixMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideAppendixMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Appendix $element, CodeBook\Appendix $expected): void
    {
        $entity = new CodeBook\Appendix();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');
        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Appendix();
        $expected = new CodeBook\Appendix();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // History
        $history = new History();
        $history->setBody('Some history');
        $element->setHistory($history);
        $expected->setHistory('Some history');
        yield 'history' => [deep_copy($element), deep_copy($expected)];

        // Objectives
        $body = new Body();
        $body->setBody('<p>Objective Body</p>');

        $objectives = new Objectives();
        $objectives->setBody($body);
        $element->setObjectives($objectives);
        $expected->setObjectives('<p>Objective Body</p>');
        yield 'objectives' => [deep_copy($element), deep_copy($expected)];

        // Objectives + TitleGroup
        $title = new Title();
        $title->setBody('Objectives Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $objectives->setTitleGroup($titleGroup);
        $expected->setObjectivesTitle('Objectives Title');
        yield 'objectivesTitle' => [deep_copy($element), deep_copy($expected)];

        // Note
        $note = new Note();
        $note->setBody('<p>Note</p>');
        $element->setNote($note);
        $expected->setNote('<p>Note</p>');
        yield 'note' => [deep_copy($element), deep_copy($expected)];

        // Note + TitleGroup
        $note->setTitleGroup($titleGroup);
        $expected->setNoteTitle('Objectives Title');
        yield 'noteTitle' => [deep_copy($element), deep_copy($expected)];

        // Abstract
        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        // Abstract + TitleGroup
        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Objectives Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        // Keywords
        $keywords = new Keywords();
        $keywords->setBody('<keyword>Key</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Key</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        // Keywords + TitleGroup
        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Objectives Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        // Body
        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];
    }
}
