<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function implode;
use function is_string;

/**
 * @since 1.11
 */
class CreditMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'credit';
    }

    public static function dtoClass(): string
    {
        return Credit::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Credit);

        $body = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif (is_string($value)) {
                $body[] = $value;
            }
        }

        $element->setBody(implode("\n", $body));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Credit);

        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());
    }
}
