<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CommonBook;
use App\Serializer\Encoder\Xml2\Element;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;

use function DeepCopy\deep_copy;

trait OverrideMapperTestCases
{
    public function titleGroupCases(AbstractElement $element, AbstractCodeBookNode $expected): iterable
    {
        /**
         * @var Element\Appendix $element
         * @var CommonBook\Field\TitleGroup $expected
         */

        $titleGroup = new TitleGroup();
        $element->setTitleGroup($titleGroup);

        $superTitle = new SuperTitle();
        $superTitle->setBody('SuperTitle');
        $titleGroup->setSuperTitle($superTitle);
        $expected->setSuperTitle('SuperTitle');

        yield 'superTitle' => [deep_copy($element), deep_copy($expected)];

        $cd = new CommitteeDesignation();
        $cd->setBody('CommitteeDesignation');
        $titleGroup->setCommitteeDesignation($cd);
        $expected->setCommitteeDesignation('CommitteeDesignation');

        yield 'committeeDesignation' => [deep_copy($element), deep_copy($expected)];

        $label = new Label();
        $label->setBody('Label');
        $titleGroup->setLabel($label);
        $expected->setLabel('Label');

        yield 'label' => [deep_copy($element), deep_copy($expected)];

        $number = new Number();
        $number->setBody('Number');
        $titleGroup->setNumber($number);
        $expected->setNumber('Number');

        yield 'number' => [deep_copy($element), deep_copy($expected)];

        $correlated = new Correlated();
        $correlated->setBody('Correlated');
        $titleGroup->setCorrelated($correlated);
        $expected->setCorrelated('Correlated');

        yield 'correlated' => [deep_copy($element), deep_copy($expected)];

        $title = new Title();
        $title->setBody('Title');
        $titleGroup->setTitle($title);
        $expected->setTitle('Title');

        yield 'title' => [deep_copy($element), deep_copy($expected)];

        $title->setTitleAbbreviation('Abbr');
        $expected->setTitleAbbreviation('Abbr');

        yield 'title@abbr' => [deep_copy($element), deep_copy($expected)];

        $title->setTitleYear('1999');
        $expected->setTitleYear('1999');

        yield 'title@year' => [deep_copy($element), deep_copy($expected)];

        $subTitle = new SubTitle();
        $subTitle->setBody('SubTitle');
        $titleGroup->setSubTitle($subTitle);
        $expected->setSubTitle('SubTitle');

        yield 'subTitle' => [deep_copy($element), deep_copy($expected)];

        $history = new History();
        $history->setBody('History');
        $titleGroup->setHistory($history);
        $expected->setTitleHistory('History');

        yield 'titleHistory' => [deep_copy($element), deep_copy($expected)];
    }
}
