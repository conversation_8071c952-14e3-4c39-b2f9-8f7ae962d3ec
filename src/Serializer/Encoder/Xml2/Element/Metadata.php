<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Metadata extends AbstractElement
{
    use Attribute\CommonAttributes;
    use HasChildren;

    public function addMeta(string $name, string $value): void
    {
        $meta = new Meta();
        $meta->setName($name);
        $meta->setBody($value);
        $this->children[] = $meta;
    }

    public function getMetaByName(string $name): ?Meta
    {
        foreach ($this->children as $i) {
            if ($i instanceof Meta && $name === $i->getName()) {
                return $i;
            }
        }
        return null;
    }
}
