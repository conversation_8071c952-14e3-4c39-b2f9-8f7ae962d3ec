<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlTableMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Table $from): Xml2\Table
    {
        $to = new Xml2\Table();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setOrientation($from->getOrientation());
        $to->setFloat($from->getFloat());
        $to->setTocEntry($from->hasTocEntry());
        $to->setPageWide($from->isPageWide());
        $to->setFrame($from->getFrame());
        $to->setColumnSeparator($from->isColumnSeparator());
        $to->setRowSeparator($from->isRowSeparator());
        $to->setBackgroundColor($from->getBackgroundColor());
        $to->setTableStyle($from->getTableStyle());
        $to->setClass($from->getClass());
        $to->setTitleAttr($from->getTitleAttr());
        $to->setSummary($from->getSummary());
        $to->setWidth($from->getWidth());
        $to->setBorder($from->getBorder());
        $to->setCellSpacing($from->getCellSpacing());
        $to->setCellPadding($from->getCellPadding());
        $to->setRules($from->getRules());

        if (!empty($from->getTable())) {
            $to->setBody($from->getTable());
        }
        if (!empty($from->getCaption())) {
            $caption = new Xml2\Caption();
            $caption->setBody($from->getCaption());
            $to->setCaption($caption);
        }
        if (!empty($from->getLegend())) {
            $to->setLegend($from->getLegend());
        }
        $to->setTableNotes($this->createTableNotes($from->getTableNotesTitle(), $from->getTableNotes()));
        if (!empty($from->getSource())) {
            $source = new Xml2\Source();
            $source->setBody($from->getSource());
            $to->setSource($source);
        }
        $to->setCredit($this->createCredit($from->getCreditTitle(), $from->getCredit()));

        return $to;
    }

    private function createTableNotes(string $title, string $body): ?Xml2\TableNotes
    {
        if (empty($title) && empty($body)) {
            return null;
        }

        $tableNotes = new Xml2\TableNotes();
        $tableNotes->setBody($body);

        if (!empty($title)) {
            $titleEl = new Xml2\Title();
            $titleEl->setBody($title);
            $titleGroup = new Xml2\TitleGroup();
            $titleGroup->setTitle($titleEl);
            $tableNotes->setTitleGroup($titleGroup);
        }

        return $tableNotes;
    }
}
