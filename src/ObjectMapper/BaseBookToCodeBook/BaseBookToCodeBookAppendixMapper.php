<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookAppendixMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\Appendix $from): CodeBook\Appendix
    {
        $to = new CodeBook\Appendix();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setIndexNumber($from->getIndexNumber());
        $to->setTocEntry($from->hasTocEntry());
        $to->setTocAutoAdd($from->isTocAutoAdd());

        $to->setHistory($from->getHistory());
        $to->setObjectivesTitle($from->getObjectivesTitle());
        $to->setObjectives($from->getObjectives());
        $to->setAbstractTitle($from->getAbstractTitle());
        $to->setAbstract($from->getAbstract());
        $to->setKeywordsTitle($from->getKeywordsTitle());
        $to->setKeywords($from->getKeywords());
        $to->setBody($from->getBody());

        if ($this->mapper) {
            $children = [];
            foreach ($from->getChildren() as $child) {
                $children[] = $this->mapper->map($child);
            }
            $to->setChildren($children);
        }

        return $to;
    }
}
