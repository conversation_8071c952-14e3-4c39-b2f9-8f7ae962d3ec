<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\Volume;
use App\Serializer\Encoder\Xml2\Mapper\VolumeMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\VolumeMapper
 */
class VolumeMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Volume::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Volume::class]);
        $this->assertInstanceOf(VolumeMapper::class, $service->classMap[Volume::class][0]);

        $this->assertArrayHasKey(VolumeMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[VolumeMapper::elementName()]);
        $this->assertInstanceOf(VolumeMapper::class, $service->elementMap[VolumeMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Volume $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<volume %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Volume();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);

        $element->setCustomerId('customer-id');
        $attrs['customer-id'] = 'customer-id';
        yield 'customer-id' => [deep_copy($element), $attrs];

        $element->setTitleType(TitleType::MANUAL);
        $attrs['title-type'] = TitleType::MANUAL;
        yield 'title-type' => [deep_copy($element), $attrs];

        $element->setGoverningType(GoverningType::MUNICIPALITY);
        $attrs['governing-type'] = GoverningType::MUNICIPALITY;
        yield 'governing-type' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Volume $element, string $innerXml): void
    {
        $xml = sprintf('<volume xmlns="%s" xmlns:m="%s">%s</volume>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Volume();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setMetadata(new Metadata());
        $innerXml = '<metadata/>';
        yield 'metadata' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setFrontMatter(new FrontMatter());
        $innerXml .= '<frontmatter/>';
        yield 'frontMatter' => [deep_copy($element), $innerXml];

        $chapter1 = new Level();
        $chapter1->setId('chapter1');
        $element->setChildren([$chapter1]);
        $innerXml .= '<level-1 id="chapter1"/>';
        yield 'chapter 1' => [deep_copy($element), $innerXml];

        $chapter2 = new Level();
        $chapter2->setId('chapter2');
        $element->setChildren([$chapter1, $chapter2]);
        $innerXml .= '<level-1 id="chapter2"/>';
        yield 'chapter 2' => [deep_copy($element), $innerXml];

        $backMatter = new BackMatter();
        $element->setBackMatter($backMatter);
        $innerXml .= '<backmatter/>';
        yield 'backMatter' => [deep_copy($element), $innerXml];
    }
}
