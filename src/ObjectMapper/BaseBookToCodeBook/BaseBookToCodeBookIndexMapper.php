<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookIndexMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\Index $from): CodeBook\Index
    {
        $to = new CodeBook\Index();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setTocEntry($from->hasTocEntry());

        if ($this->mapper) {
            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }
        }

        return $to;
    }
}
