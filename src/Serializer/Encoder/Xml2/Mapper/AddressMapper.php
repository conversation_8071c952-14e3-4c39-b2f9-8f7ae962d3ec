<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Address;
use App\Serializer\Encoder\Xml2\Element\Email;
use App\Serializer\Encoder\Xml2\Element\Url;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function is_string;
use function Sabre\Xml\Deserializer\keyValue;

class AddressMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'address';
    }

    public static function dtoClass(): string
    {
        return Address::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Address);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('add-line' === $key && is_string($value)) {
                $element->setAddressLine($value);
            } elseif ('orgname' === $key && is_string($value)) {
                $element->setOrganizationName($value);
            } elseif ('street' === $key && is_string($value)) {
                $element->setStreet($value);
            } elseif ('city' === $key && is_string($value)) {
                $element->setCity($value);
            } elseif ('state' === $key && is_string($value)) {
                $element->setState($value);
            } elseif ('country' === $key && is_string($value)) {
                $element->setCountry($value);
            } elseif ('postalcode' === $key && is_string($value)) {
                $element->setPostalCode($value);
            } elseif ('email' === $key && $value instanceof Email) {
                $element->setEmail($value);
            } elseif ('url' === $key && $value instanceof Url) {
                $element->setUrl($value);
            } elseif ('phone' === $key && is_string($value)) {
                $element->setPhone($value);
            } elseif ('fax' === $key && is_string($value)) {
                $element->setFax($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Address);

        $writeXml = function ($name, $innerXml) use ($writer) {
            if (!empty($innerXml)) {
                $writer->startElement($name);
                $writer->writeRaw($innerXml);
                $writer->endElement();
            }
        };

        $writeXml(self::CLARK . 'add-line', $element->getAddressLine());
        $writeXml(self::CLARK . 'orgname', $element->getOrganizationName());
        $writeXml(self::CLARK . 'street', $element->getStreet());
        $writeXml(self::CLARK . 'city', $element->getCity());
        $writeXml(self::CLARK . 'state', $element->getState());
        $writeXml(self::CLARK . 'postalcode', $element->getPostalCode());
        $writeXml(self::CLARK . 'country', $element->getCountry());

        if ($element->getEmail()) {
            $writer->writeElement(self::CLARK . 'email', $element->getEmail());
        }
        if ($element->getUrl()) {
            $writer->writeElement(self::CLARK . 'url', $element->getUrl());
        }

        $writeXml(self::CLARK . 'phone', $element->getPhone());
        $writeXml(self::CLARK . 'fax', $element->getFax());
    }
}
