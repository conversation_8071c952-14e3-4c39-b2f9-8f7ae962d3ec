<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Helper\Xml2Helper;
use DOMElement;
use DOMXPath;
use Symfony\Contracts\Service\Attribute\Required;
use App\Entity\CodeBook\Equation;
use App\Entity\CodeBook\Section;
use Doctrine\ORM\EntityManagerInterface;

class EquationService extends AbstractNodeService
{
    private Xml2Helper $xml2Helper;

    #[Required]
    public function setXml2Helper(Xml2Helper $xml2Helper): void
    {
        $this->xml2Helper = $xml2Helper;
    }

    /**
     * Process equations in section body and return updated body
     * Returns null if no changes were made
     */
    public function processEquationsInSection(Section $section): ?string
    {
        $body = $section->getBody();
        $hasChanges = false;
        $equationMap = [];

        if (!$body || !$this->hasEquationTags($body)) {
            return null;
        }

        try{
            $dom = $this->xml2Helper->createDOMDocument($body, true);
            $xpath = $this->xml2Helper->createDOMXPath($dom);
            $equationElements = $xpath->query('//*[local-name()="equation"]');

            foreach ($equationElements as $equationElement) {
                $this->processEquationElement($equationElement, $section, $hasChanges, $equationMap, $xpath);
            }
            if (!$hasChanges) {
                return null;
            }

            $newEquations = array_filter(
                $equationMap,
                fn($item) => $item['isNew']
            );

            foreach ($newEquations as $item) {
                $this->em->persist($item['equation']);
            }

            if (!empty($newEquations)) {
                $this->em->flush();
            }

            $finalBody = $dom->saveXML($dom->documentElement);

            foreach ($equationMap as $placeholder => $item) {
                $equation = $item['equation'];
                $replacement = '<equation ct-uuid="' . $equation->getId() . '"></equation>';
                $finalBody = str_replace($placeholder, $replacement, $finalBody);
            }
            return $finalBody !== $body ? $finalBody : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function hasEquationTags(string $body): bool
    {
        return str_contains($body, '<equation');
    }

    private function extractEquationId(string $attributes): ?string
    {
        if (preg_match('/ct-uuid="([^"]+)"/', $attributes, $m)) {
            return $m[1];
        }
        if (preg_match('/id="([^"]+)"/', $attributes, $m)) {
            return $m[1];
        }
        return null;
    }

    private function extractEquationData(string $innerHtml): array
    {
        return [
            'label'     => $this->extractField('<label\b[^>]*>(.*?)</label>', $innerHtml),
            'number'    => $this->extractField('<number\b[^>]*>(.*?)</number>', $innerHtml),
            'math'      => $this->extractField('<math\b[^>]*>(.*?)</math>', $innerHtml),
            'whereList' => $this->extractField('<where-list\b[^>]*>(.*?)</where-list>', $innerHtml),
        ];
    }

    private function extractField(string $pattern, string $content): ?string
    {
        if (preg_match('#' . $pattern . '#si', $content, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    private function isEquationDataEmpty(array $data): bool
    {
        return empty($data['label'])
            && empty($data['number'])
            && empty($data['math'])
            && empty($data['whereList']);
    }

    /**
     * Process a single equation element
    */
    private function processEquationElement(DOMElement $element,  Section $section,  bool &$hasChanges, array &$equationMap, DOMXPath $xpath ): void
    {
        $id = $element->getAttribute('ct-uuid') ?: $element->getAttribute('id') ?: null;
        $data = $this->extractEquationDataFromElement($element, $xpath);

        if ($this->isEquationDataEmpty($data)) {
            if ($id) {
                $this->clearElementContent($element);
                $element->setAttribute('ct-uuid', $id);
            } else {
                if ($element->parentNode) {
                    $element->parentNode->removeChild($element);
                }
            }
            return;
        }
        $equation = null;
        if ($id) {
            $equation = $this->em->getRepository(Equation::class)->findOneBy(['nodeId' => $id]);
        }

        $isNew = false;
        if (!$equation) {
            $equation = new Equation();
            $equation->setParent($section);
            $isNew = true;
        }
        if ($this->updateEquationFields($equation, $data)) {
            $hasChanges = true;
        }

        $placeholder = '###EQUATION_' . uniqid('', true) . '###';
        $equationMap[$placeholder] = ['equation' => $equation, 'isNew' => $isNew];

        $placeholderNode = $element->ownerDocument->createTextNode($placeholder);
        $element->parentNode->replaceChild($placeholderNode, $element);
    }

    /**
     * Extract equation data from DOM element
     */
    private function extractEquationDataFromElement(DOMElement $element, DOMXPath $xpath): array
    {
        return [
            'label'     => $this->getElementText($element, 'label', $xpath),
            'number'    => $this->getElementText($element, 'number', $xpath),
            'math'      => $this->getElementText($element, 'math', $xpath),
            'whereList' => $this->getElementText($element, 'where-list', $xpath),
        ];
    }

    /**
     * Get text content of first child element with given tag name
     */
    private function getElementText(DOMElement $parent, string $tagName, DOMXPath $xpath): ?string
    {
        $elements = $xpath->query('./*[local-name()="' . $tagName . '"]', $parent);

        if ($elements->length === 0) {
            return null;
        }

        $content = $this->getInnerXml($elements->item(0));
        return empty(trim($content)) ? null : trim($content);
    }

    private function getInnerXml(DOMElement $element): string
    {
        $innerXml = '';
        foreach ($element->childNodes as $child) {
            $innerXml .= $element->ownerDocument->saveXML($child);
        }
        return $innerXml;
    }

    private function clearElementContent(DOMElement $element): void
    {
        while ($element->firstChild) {
            $element->removeChild($element->firstChild);
        }
    }

    private function updateEquationFields(Equation $equation, array $data): bool
    {
        $changed = false;

        if ($equation->getLabel() !== ($data['label'] ?? '')) {
            $equation->setLabel($data['label'] ?? '');
            $changed = true;
        }

        if ($equation->getNumber() !== ($data['number'] ?? '')) {
            $equation->setNumber($data['number'] ?? '');
            $changed = true;
        }

        if ($equation->getMath() !== ($data['math'] ?? '')) {
            $equation->setMath($data['math'] ?? '');
            $changed = true;
        }

        if ($equation->getWhereList() !== ($data['whereList'] ?? '')) {
            $equation->setWhereList($data['whereList'] ?? '');
            $changed = true;
        }
        return $changed;
    }

    /**
     * Get Equation data from section content and replace it with equation
     *
     * @param string $section
     */
    public function processEquationEntityInSection(string $section): string
    {
        if (empty($section)) {
            return $section;
        }

        try {
            $dom = $this->xml2Helper->createDOMDocument($section, true);
            $xpath = $this->xml2Helper->createDOMXPath($dom);
            $equationElements = $xpath->query('//*[local-name()="equation" and @ct-uuid]');

            if ($equationElements->length === 0) {
                return $section;
            }

            foreach ($equationElements as $equation) {
                $equationId = $equation->getAttribute('ct-uuid');
                $equationData = $this->getEquationsData($equationId);
                if ($equationData) {
                    if (!empty($equationData->getWhereList())) {
                        $equationData->setWhereList($this->processEquationEntityInSection($equationData->getWhereList()));
                    }
                    $this->prepareEquation($equation, $equationData);
                }
            }
            return $this->xml2Helper->getInnerXml($dom->documentElement);
        } catch (\Exception $e) {
            return $section;
        }
    }

    /**
     * Get Equation form DB
     *
     * @param string $equationsId
     * @return Equation
     */
    private function getEquationsData (string $equationsId): ?Equation {
        return $this->em->getRepository(Equation::class)->find($equationsId);
    }

    /**
     * Prepare the equation XML
     *
     * @param DOMElement $equation
     * @param Equation $equationData
     * @return DOMElement
     */
    private function prepareEquation(DOMElement $equation, Equation $equationData): DOMElement {
        $doc = $equation->ownerDocument;
        $equation->setAttribute('id', $equationData->getUlid());
        $equation->removeAttribute('ct-uuid');

        if ($equationData->getLabel()) {
            $label = $doc->createElement('label');
            $this->appendFragment($doc, $label, $equationData->getLabel());
            $equation->appendChild($label);
        }

        if ($equationData->getNumber()) {
            $number = $doc->createElement('number');
            $this->appendFragment($doc, $number, $equationData->getNumber());
            $equation->appendChild($number);
        }

        if ($equationData->getMath()) {
            $math = $doc->createElement('math');
            $this->appendFragment($doc, $math, $equationData->getMath());
            $equation->appendChild($math);
        }

        if ($equationData->getWhereList()) {
            $whereList = $doc->createElement('where-list');

            $xmlFragment = '<wrapper>' . $equationData->getWhereList() . '</wrapper>';
            $tempDom = new \DOMDocument('1.0', 'UTF-8');
            @$tempDom->loadXML($xmlFragment);
            foreach ($tempDom->documentElement->childNodes as $child) {
                $whereList->appendChild($doc->importNode($child, true));
            }

            $equation->appendChild($whereList);
        }
        return $equation;
    }

    /**
     * Append Valid XML fragment
     *
     * @param \DOMDocument $doc
     * @param DOMElement $parent
     * @param string $xmlString
     * @return void
     */
    private function appendFragment(\DOMDocument $doc, DOMElement $parent, string $xmlString): void
    {
        $fragment = $doc->createDocumentFragment();

        if (@$fragment->appendXML($xmlString)) {
            $parent->appendChild($fragment);
        } else {
            $parent->appendChild($doc->createTextNode($xmlString));
        }
    }

    /**
     * Update Existing Equation from Section
     *
     * @param Section $section
     * @return void
     */
    public function updateExistingEquation(Section $section): void
    {
        if (empty($section)) {
            return;
        }

        try {
            $dom = $this->xml2Helper->createDOMDocument($section->getBody(), true);
            $xpath = $this->xml2Helper->createDOMXPath($dom);
            $equationElements = $xpath->query('//*[local-name()="equation" and @id and not(ancestor::*[local-name()="equation"])]');

            if ($equationElements->length === 0) {
                return ;
            }

            foreach ($equationElements as $equation) {
                $data = $this->extractEquationDataFromElement($equation, $xpath);
                if (!empty($data['whereList'])) {
                    $data['whereList'] = $this->processWhereList($data['whereList'], $section);
                }
                if (!$this->isEquationDataEmpty($data)) {
                    $newEquation = new Equation();
                    $newEquation->setParent($section);
                    $this->updateEquationFields($newEquation, $data);

                    while ($equation->hasChildNodes()) {
                        $equation->removeChild($equation->firstChild);
                    }
                    $equation->appendChild($dom->createTextNode(''));

                    $this->em->persist($newEquation);
                    $this->em->flush();

                    $equation->setAttribute('ct-uuid', (string) $newEquation->getId());
                    $equation->removeAttribute('id');
                }
            }
            $section->setBody($this->xml2Helper->getInnerXml($dom->documentElement));
            $this->em->persist($section);
            $this->em->flush();
        } catch (\Exception $e) {
            return;
        }
    }

    /**
     * Process where-list content to extract equations
     *
     * @param string $whereList
     * @param Section $section
     * @return string
     */
    private function processWhereList(string $whereList, Section $section): string
    {
        if (empty($whereList)) {
            return $whereList;
        }
        try {
            $dom = $this->xml2Helper->createDOMDocument($whereList, true);
            $xpath = $this->xml2Helper->createDOMXPath($dom);
            $equationElements = $xpath->query('//*[local-name()="equation" and @id and not(ancestor::*[local-name()="equation"])]');

            if ($equationElements->length === 0) {
                return $whereList;
            }
            foreach ($equationElements as $equation) {
                $data = $this->extractEquationDataFromElement($equation, $xpath);

                if (!empty($data['whereList'])) {
                    $data['whereList'] = $this->processWhereList($data['whereList'], $section);
                }

                if (!$this->isEquationDataEmpty($data)) {
                    $newEquation = new Equation();
                    $newEquation->setParent($section);
                    $this->updateEquationFields($newEquation, $data);

                    while ($equation->hasChildNodes()) {
                        $equation->removeChild($equation->firstChild);
                    }
                    $equation->appendChild($dom->createTextNode(''));

                    $this->em->persist($newEquation);
                    $this->em->flush();

                    $equation->setAttribute('ct-uuid', (string) $newEquation->getId());
                    $equation->removeAttribute('id');
                }
            }
            return $this->xml2Helper->getInnerXml($dom->documentElement);

        } catch (\Exception $e) {
            return $whereList;
        }
    }
}
