<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\keyValue;

class ReferenceMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'ref';
    }

    public static function dtoClass(): string
    {
        return Reference::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Reference);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('nav-pointer-group' === $key && is_string($value)) {
                $element->setNavPointerGroup($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Reference);

        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        if (!empty($element->getNavPointerGroup())) {
            $writer->writeRaw($element->getNavPointerGroup());
        }
    }
}
