<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class AbstractBlockElement extends AbstractElement
{
    private string $outerXml = '';

    public function getOuterXml(): string
    {
        return $this->outerXml;
    }

    public function setOuterXml(string $outerXml): void
    {
        $this->outerXml = $outerXml;
    }
}
