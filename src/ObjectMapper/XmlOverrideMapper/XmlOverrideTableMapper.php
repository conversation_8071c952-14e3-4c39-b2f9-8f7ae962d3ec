<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Table;
use App\Serializer\Encoder\Xml2\Element;
use Doctrine\ORM\EntityManagerInterface;

class XmlOverrideTableMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Table $element, CodeBook\Table $entity): CodeBook\Table
    {
        $this->mapTitleGroup($element, $entity);
//        TODO: Investigate why it's not serializing the body
//        $entity->setTable($element->getBody());

        if ($tableNotes = $element->getTableNotes()) {
            $entity->setTableNotes($tableNotes->getBody());

            if ($tableNotes->getTitleGroup() && $title = $tableNotes->getTitleGroup()->getTitle()) {
                $entity->setTableNotesTitle($title->getBody());
            }
        }

        return $entity;
    }
}
