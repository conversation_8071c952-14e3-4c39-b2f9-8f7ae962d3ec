<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideReferenceStandardMapper;
use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\Title;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideReferenceStandardMapper */
class XmlOverrideReferenceStandardMapperTest extends KernelTestCase
{
    private ?XmlOverrideReferenceStandardMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideReferenceStandardMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Reference $element, CodeBook\ReferenceStandard $expected): void
    {
        $entity = new CodeBook\ReferenceStandard();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Reference();
        $expected = new CodeBook\ReferenceStandard();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // TitleGroup with Number
        $number = new Number();
        $number->setBody('RS-101');
        $titleGroup = new TitleGroup();
        $titleGroup->setNumber($number);
        $element->setTitleGroup($titleGroup);
        $expected->setNumber('RS-101');
        yield 'titleGroup-number' => [deep_copy($element), deep_copy($expected)];

        // TitleGroup with Title
        $title = new Title();
        $title->setBody('Reference Standard Title');
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);
        $expected->setTitle('Reference Standard Title');
        yield 'titleGroup-title' => [deep_copy($element), deep_copy($expected)];

        // TitleGroup with both Number and Title
        $number2 = new Number();
        $number2->setBody('RS-202');
        $title2 = new Title();
        $title2->setBody('Updated Standard');
        $titleGroup2 = new TitleGroup();
        $titleGroup2->setNumber($number2);
        $titleGroup2->setTitle($title2);
        $element->setTitleGroup($titleGroup2);
        $expected->setNumber('RS-202');
        $expected->setTitle('Updated Standard');
        yield 'titleGroup-number+title' => [deep_copy($element), deep_copy($expected)];
    }
}
