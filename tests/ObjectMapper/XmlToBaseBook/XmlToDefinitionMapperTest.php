<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToDefinitionMapper;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\Term;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToDefinitionMapper */
class XmlToDefinitionMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToDefinitionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToDefinitionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(DefinitionItem $element, BaseBook\Definition $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new DefinitionItem();
        $expected = new BaseBook\Definition();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);

        $cd = new CommitteeDesignation();
        $cd->setBody('CommitteeDesignation');
        $element->setCommitteeDesignation($cd);
        $expected->setCommitteeDesignation('CommitteeDesignation');
        yield 'committeeDesignation' => [deep_copy($element), deep_copy($expected)];

        $term = new Term();
        $term->setBody('Term');
        $element->setTerm($term);
        $expected->setTerm('Term');
        yield 'term' => [deep_copy($element), deep_copy($expected)];

        $definition = new Definition();
        $definition->setBody('Definition');
        $element->setDefinition($definition);
        $expected->setDefinition('Definition');
        yield 'definition' => [deep_copy($element), deep_copy($expected)];
    }
}
