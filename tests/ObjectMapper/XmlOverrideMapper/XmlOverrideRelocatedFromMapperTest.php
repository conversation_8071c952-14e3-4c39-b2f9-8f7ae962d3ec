<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideRelocatedFromMapper;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideRelocatedFromMapper */
class XmlOverrideRelocatedFromMapperTest extends KernelTestCase
{
    private ?XmlOverrideRelocatedFromMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideRelocatedFromMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(RelocatedFrom $element, CodeBook\RelocatedFrom $expected): void
    {
        $entity = new CodeBook\RelocatedFrom();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new RelocatedFrom();
        $expected = new CodeBook\RelocatedFrom();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // Indent = true
        $element->setIndent(true);
        $expected->setIndent(true);
        yield 'indent-true' => [deep_copy($element), deep_copy($expected)];

        // Align
        $element->setAlign('center');
        $expected->setAlign('center');
        yield 'align' => [deep_copy($element), deep_copy($expected)];

        // ReferenceId
        $element->setReferenceId('ref-001');
        $expected->setReferenceId('ref-001');
        yield 'referenceId' => [deep_copy($element), deep_copy($expected)];

        // All fields together
        $element->setIndent(true);
        $element->setAlign('left');
        $element->setReferenceId('RF-202');
        $expected->setIndent(true);
        $expected->setAlign('left');
        $expected->setReferenceId('RF-202');
        yield 'all-fields' => [deep_copy($element), deep_copy($expected)];
    }
}
