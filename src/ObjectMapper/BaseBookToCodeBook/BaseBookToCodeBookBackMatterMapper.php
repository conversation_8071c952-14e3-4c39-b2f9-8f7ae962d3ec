<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookBackMatterMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\BackMatter $from): CodeBook\BackMatter
    {
        $to = new CodeBook\BackMatter();
        $this->mapCommonAttributes($from, $to);
        $this->mapQrCode($from, $to);

        if ($this->mapper) {
            $children = [];
            foreach ($from->getChildren() as $child) {
                $children[] = $this->mapper->map($child);
            }
            $to->setChildren($children);

            foreach ($children as $child) {
                if ($child instanceof CodeBook\Index) {
                    $to->setIndex($child);
                    break;
                }
            }
        }

        return $to;
    }
}
