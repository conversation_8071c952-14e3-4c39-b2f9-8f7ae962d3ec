<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function implode;
use function is_string;

/**
 * @since 1.11
 */
class TableNotesMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'table-notes';
    }

    public static function dtoClass(): string
    {
        return TableNotes::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof TableNotes);

        $notes = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('table-note' === $key && is_string($value)) {
                $notes[] = $value;
            }
        }

        $element->setBody(implode("\n", $notes));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof TableNotes);

        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());
    }
}
