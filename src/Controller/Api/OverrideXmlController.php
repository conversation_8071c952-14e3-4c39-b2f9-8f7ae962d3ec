<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api;

use App\Dto\Xml2\OverrideXml\OverrideXmlRequest;
use App\Dto\Xml2\OverrideXml\UpdateOverrideXmlRequest;
use App\Dto\Xml2\OverrideXml\ValidateXmlRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Service\Xml2\OverrideXmlService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Nelmio\ApiDocBundle\Annotation\Model;

#[Security("is_granted('ROLE_SUPER_ADMIN')")]
#[Route(path: '/api/v2/override-xml/{shortCode}')]
#[OA\Tag(name: 'Override XML')]
#[Nelmio\Security(name: 'Bearer')]
#[ParamConverter('project', options: ['mapping' => ['shortCode' => 'shortCode']])]
#[ParamConverter('node', options: ['mapping' => ['nodeId' => 'nodeId']])]
class OverrideXmlController extends AbstractController
{
    public function __construct(
        private readonly OverrideXmlService $overrideXmlService
    ) {}

    #[Route(path: '/chapters/{nodeId}', name: 'app_api_xml2_override_getNodeOverrideXml', methods: ['GET'])]
    #[OA\Get(
        description: 'Returns the XML representation (optionally including notes or children).',
        summary: 'Get Override XML for a specific node',
        parameters: [
            new OA\Parameter(name: 'shortCode', in: 'path', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'nodeId', in: 'path', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(
                name: 'notes',
                description: 'Include notes of a specific type in the XML output.',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['internal', 'pubs', 'typesetter', 'all', 'none'])
            ),
            new OA\Parameter(
                name: 'download',
                description: 'If true, return as file download.',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'boolean', default: false)
            ),
            new OA\Parameter(
                name: 'overrideChildren',
                description: 'If true, include children in the XML output.',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'boolean', default: false)
            ),
        ],
        responses: [
            new OA\Response(response: 200, description: 'XML content successfully retrieved'),
            new OA\Response(response: 400, description: 'Invalid parameters'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'Node not found')
        ]
    )]

    public function getNodeOverrideXml(
        Project $project,
        AbstractCodeBookNode $node,
        OverrideXmlRequest $requestDto
    ): Response {
        return $this->overrideXmlService->getOverrideXml($node, $requestDto);
    }

    #[Route(path: '/update/{nodeId}', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update Override XML for a specific node',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Model(type: UpdateOverrideXmlRequest::class))
        ),
        responses: [
            new OA\Response(response: 200, description: 'Node XML successfully updated'),
            new OA\Response(response: 404, description: 'Node not found'),
            new OA\Response(response: 400, description: 'Invalid request body')
        ]
    )]
    public function update(Project $project, UpdateOverrideXmlRequest $requestDto): Response
    {
        $result = $this->overrideXmlService->updateNodeOverrideXml($requestDto);

        return $this->json($result, Response::HTTP_OK);
    }

    #[Route(path: '/validate/{nodeId}', methods: ['POST'])]
    #[OA\Post(
        summary: 'Validate XML content for a specific node',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Model(type: ValidateXmlRequest::class))
        ),
        responses: [
            new OA\Response(response: 200, description: 'XML content is valid'),
            new OA\Response(response: 400, description: 'Invalid XML content'),
            new OA\Response(response: 404, description: 'Node not found')
        ]
    )]
    public function validate(Project $project, ValidateXmlRequest $requestDto): Response
    {
        $result = $this->overrideXmlService->validateNodeXml($requestDto);

        return $this->json($result, Response::HTTP_OK);
    }


}
