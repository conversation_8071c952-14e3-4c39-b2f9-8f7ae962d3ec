<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

use function assert;

class BaseBookToCodeBookVolumeMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\Volume $from): CodeBook\Volume
    {
        $to = new CodeBook\Volume();
        $this->mapCommonAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setCustomerId($from->getCustomerId());
        $to->setTitleType($from->getTitleType());
        $to->setGoverningType($from->getGoverningType());

        $to->setMetaTitle($from->getMetaTitle());
        $to->setEdition($from->getEdition());
        $to->setParentDocument($from->getParentDocument());
        $to->setPublicationId($from->getPublicationId());
        $to->setYear($from->getYear());
        $to->setPublicationAbbreviation($from->getPublicationAbbreviation());
        $to->setVersion($from->getVersion());
        $to->setDateOrigin($from->getDateOrigin());
        $to->setOrigin($from->getOrigin());
        $to->setDateUpdated($from->getDateUpdated());
        $to->setModifiedBy($from->getModifiedBy());

        if ($this->mapper) {
            if ($from->getFrontMatter()) {
                $frontMatter = $this->mapper->map($from->getFrontMatter());
                assert($frontMatter instanceof CodeBook\FrontMatter);
                $to->addChild($frontMatter);
            }

            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }

            if ($from->getBackMatter()) {
                $backMatter = $this->mapper->map($from->getBackMatter());
                assert($backMatter instanceof CodeBook\BackMatter);
                $to->addChild($backMatter);
            }
        }

        return $to;
    }
}
