<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\CodeBook;

use App\Entity\IdTrait;
use Doctrine\ORM\Mapping as ORM;
use Ulid\Ulid;

#[ORM\Entity]
#[ORM\Table(name: 'code_book_link_publication_ref')]
class PublicationRefLink
{
    use IdTrait;

    #[ORM\Column(type: 'string', length: 36, unique: true)]
    private string $uuid;

    #[ORM\ManyToOne(targetEntity: Publication::class, inversedBy: 'allPublicationRefLinks')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Publication $codeBook = null;

    #[ORM\ManyToOne(targetEntity: AbstractCodeBookNode::class, inversedBy: 'publicationRefLinks')]
    #[ORM\JoinColumn(nullable: false)]
    private ?AbstractCodeBookNode $fromNode = null;

    #[ORM\Column(type: 'text')]
    private string $publicationId = '';

    #[ORM\Column(type: 'text')]
    private string $internalId = '';

    #[ORM\Column(type: 'text')]
    private string $role = '';

    #[ORM\Column(type: 'text')]
    private string $body = '';

    #[ORM\Column(type: 'text')]
    private string $originalBody = '';

    #[ORM\Column(type: 'boolean', options: ['default' => true])]
    private bool $validPublicationId = true;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $customBody = false;

    public function __construct()
    {
        $this->uuid = (string) Ulid::generate();
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    public function setUuid(string $uuid): void
    {
        $this->uuid = $uuid;
    }

    public function getCodeBook(): ?Publication
    {
        return $this->codeBook;
    }

    public function setCodeBook(?Publication $codeBook): void
    {
        $this->codeBook = $codeBook;
    }

    public function getFromNode(): ?AbstractCodeBookNode
    {
        return $this->fromNode;
    }

    public function setFromNode(?AbstractCodeBookNode $fromNode): void
    {
        $this->fromNode = $fromNode;
    }

    public function getPublicationId(): string
    {
        return $this->publicationId;
    }

    public function setPublicationId(string $publicationId): void
    {
        $this->publicationId = $publicationId;
    }

    public function getInternalId(): string
    {
        return $this->internalId;
    }

    public function setInternalId(string $internalId): void
    {
        $this->internalId = $internalId;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): void
    {
        $this->body = $body;
    }

    public function getOriginalBody(): string
    {
        return $this->originalBody;
    }

    public function setOriginalBody(string $originalBody): void
    {
        $this->originalBody = $originalBody;
    }

    public function isValidPublicationId(): bool
    {
        return $this->validPublicationId;
    }

    public function setValidPublicationId(bool $valid): void
    {
        $this->validPublicationId = $valid;
    }

    public function isCustomBody(): bool
    {
        return $this->customBody;
    }

    public function setCustomBody(bool $customBody): void
    {
        $this->customBody = $customBody;
    }
}
