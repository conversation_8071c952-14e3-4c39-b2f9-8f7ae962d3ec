<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideIndexDivisionMapper;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideIndexDivisionMapper */
class XmlOverrideIndexDivisionMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideIndexDivisionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideIndexDivisionMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(IndexDivision $element, CodeBook\IndexDivision $expected): void
    {
        $entity = new CodeBook\IndexDivision();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new IndexDivision();
        $expected = new CodeBook\IndexDivision();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // TitleGroup with Title only
        $title = new Title();
        $title->setBody('Division Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);
        $expected->setTitle('Division Title');

        yield 'titleGroup' => [deep_copy($element), deep_copy($expected)];
    }
}
