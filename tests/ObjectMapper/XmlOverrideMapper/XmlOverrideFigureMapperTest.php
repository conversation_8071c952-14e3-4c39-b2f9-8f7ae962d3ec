<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideFigureMapper;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\FigureNotes;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideFigureMapper */
class XmlOverrideFigureMapperTest extends KernelTestCase
{
    private ?XmlOverrideFigureMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideFigureMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Figure $element, CodeBook\Figure $expected): void
    {
        $entity = new CodeBook\Figure();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Figure();
        $expected = new CodeBook\Figure();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // Orientation
        $element->setOrientation('landscape');
        $expected->setOrientation('landscape');
        yield 'orientation' => [deep_copy($element), deep_copy($expected)];

        // Caption
        $caption = new Caption();
        $caption->setBody('Figure caption text');
        $element->setCaption($caption);
        $expected->setCaption('Figure caption text');
        yield 'caption' => [deep_copy($element), deep_copy($expected)];

        // FigureNotes
        $notes = new FigureNotes();
        $notes->setBody('Some notes');
        $element->setFigureNotes($notes);
        $expected->setFigureNotes('Some notes');
        yield 'figureNotes' => [deep_copy($element), deep_copy($expected)];

        // FigureNotes + TitleGroup
        $title = new Title();
        $title->setBody('Notes Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $notes->setTitleGroup($titleGroup);
        $expected->setFigureNotesTitle('Notes Title');
        yield 'figureNotesTitle' => [deep_copy($element), deep_copy($expected)];

        // Legend
        $element->setLegend('Figure legend text');
        $expected->setLegend('Figure legend text');
        yield 'legend' => [deep_copy($element), deep_copy($expected)];

        // Source
        $source = new Source();
        $source->setBody('Source text');
        $element->setSource($source);
        $expected->setSource('Source text');
        yield 'source' => [deep_copy($element), deep_copy($expected)];

        // Credit
        $credit = new Credit();
        $credit->setBody('Credit text');
        $element->setCredit($credit);
        $expected->setCredit('Credit text');
        yield 'credit' => [deep_copy($element), deep_copy($expected)];

        // Credit + TitleGroup
        $credit->setTitleGroup($titleGroup);
        $expected->setCreditTitle('Notes Title');
        yield 'creditTitle' => [deep_copy($element), deep_copy($expected)];
    }
}
