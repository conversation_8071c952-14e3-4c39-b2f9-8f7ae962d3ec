<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\PublicationRefLink;
use App\Helper\Xml2Helper;
use App\Message\CodeBook\UpsertPublicationRefLinksAsyncMessage;
use App\Service\StopwatchService;
use Doctrine\ORM\EntityManagerInterface;
use DOMElement;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Stopwatch\StopwatchEvent;

use function iterator_to_array;
use function mb_strpos;

#[AsMessageHandler]
class UpsertPublicationRefLinksAsyncMessageHandler
{
    use ParseFieldsTrait;

    private StopwatchEvent $stopwatch;
    private bool $needsFlush = false;
    private bool $shouldFlush = true;

    public function __construct(
        private readonly EntityManagerInterface $em,
        StopwatchService $stopwatch,
    ) {
        $this->stopwatch = $stopwatch->create('upsert.publication-ref-links');
    }

    public function __invoke(UpsertPublicationRefLinksAsyncMessage $message): void
    {
        $nodeId = $message->getNodeId();
        $node = $this->em->find(AbstractCodeBookNode::class, $nodeId);
        if (!$node) {
            return;
        }

        $this->stopwatch->start();
        $this->needsFlush = false;

        $this->parseFields($node, [$this, 'updateXml']);

        if ($this->needsFlush && $this->shouldFlush) {
            $this->em->flush();
        }

        $this->needsFlush = false;
        $this->stopwatch->stop();
    }

    public function setAutoFlush(bool $autoFlush): void
    {
        $this->shouldFlush = $autoFlush;
    }

    protected function updateXml(string $xml, AbstractCodeBookNode $codeBookNode): string
    {
        if (false === mb_strpos($xml, '<publication-ref ')) {
            return $xml;
        }

        // <publication-ref role="ICC-publication" publication-id="IBC2024P1" internal-id="Ch02">Chapter 2</publication-ref>
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $links = $xpath->query('//x:publication-ref', $document->documentElement);
        if ($links->length === 0) {
            return $xml;
        }

        $existing = [];
        foreach ($codeBookNode->getPublicationRefLinks() as $i) {
            $existing[$i->getUuid()] = $i;
        }

        /** @var DOMElement[] $linksArr */
        $linksArr = iterator_to_array($links);
        foreach ($linksArr as $link) {
            $uuid = $link->getAttribute('ct-uuid');
            if ($uuid && isset($existing[$uuid])) {
                $entity = $existing[$uuid];
                unset($existing[$uuid]);
            } else {
                $entity = $this->createPublicationRef($codeBookNode, $link);
                $this->needsFlush = true;
            }

            $this->parseBody($entity, $link);
        }

        // Remove orphaned links
        foreach ($existing as $orphan) {
            $codeBookNode->removePublicationRefLink($orphan);
            $this->em->remove($orphan);
            $this->needsFlush = true;
        }

        return Xml2Helper::getInnerXml($document->documentElement);
    }

    protected function createPublicationRef(AbstractCodeBookNode $codeBookNode, DOMElement $link): PublicationRefLink
    {
        $publicationId = $link->getAttribute('publication-id');
        $internalId = $link->getAttribute('internal-id');
        $role = $link->getAttribute('role');

        $entity = new PublicationRefLink();
        $entity->setCodeBook($codeBookNode->getPublication());
        $entity->setPublicationId($publicationId);
        $entity->setInternalId($internalId);
        $entity->setRole($role);
        $entity->setValidPublicationId(true);

        $this->parseBody($entity, $link);

        $this->em->persist($entity);
        $codeBookNode->addPublicationRefLink($entity);

        $link->setAttribute('ct-uuid', $entity->getUuid());

        return $entity;
    }

    protected function parseBody(PublicationRefLink $entity, DOMElement $link): void
    {
        $body = '';
        foreach ($link->childNodes as $child) {
            $body .= $link->ownerDocument->saveXML($child);
        }

        $entity->setBody($body);
        $entity->setOriginalBody($body);
    }
}
