<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlTitlePageMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\TitlePage $from): Xml2\TitlePage
    {
        $to = new Xml2\TitlePage();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);

        return $to;
    }
}
