<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\References;
use App\Serializer\Encoder\Xml2\Mapper\ReferencesMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\ReferencesMapper
 */
class ReferencesMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(References::class, $service->classMap);
        $this->assertIsCallable($service->classMap[References::class]);
        $this->assertInstanceOf(ReferencesMapper::class, $service->classMap[References::class][0]);

        $this->assertArrayHasKey(ReferencesMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[ReferencesMapper::elementName()]);
        $this->assertInstanceOf(ReferencesMapper::class, $service->elementMap[ReferencesMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(References $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<references %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new References();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(References $element, string $innerXml): void
    {
        $xml = sprintf('<references xmlns="%s" xmlns:m="%s">%s</references>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new References();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $ref1 = new Reference();
        $ref1->setId('ref1');
        $element->setChildren([$ref1]);
        $innerXml .= '<ref id="ref1"/>';
        yield 'chapter 1' => [deep_copy($element), $innerXml];

        $ref2 = new Reference();
        $ref2->setId('ref2');
        $element->setChildren([$ref1, $ref2]);
        $innerXml .= '<ref id="ref2"/>';
        yield 'chapter 2' => [deep_copy($element), $innerXml];
    }
}
