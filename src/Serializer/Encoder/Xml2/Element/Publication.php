<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

use App\Serializer\Encoder\Xml2\Xml2Schema;

/**
 * @since 1.11
 */
class Publication extends AbstractElement
{
    use Attribute\CommonAttributes;
    use HasChildren;

    public float $schemaVersion = Xml2Schema::VERSION;
    public float $schematronVersion = 0;
    public float $documentVersion = 0;
    public ?Metadata $metadata = null;

    public function getSchemaVersion(): float
    {
        return $this->schemaVersion;
    }

    public function setSchemaVersion(float $schemaVersion): void
    {
        $this->schemaVersion = $schemaVersion;
    }

    public function getDocumentVersion(): float
    {
        return $this->documentVersion;
    }

    public function getSchematronVersion(): float
    {
        return $this->schematronVersion;
    }

    public function setSchematronVersion(float $schematronVersion): void
    {
        $this->schematronVersion = $schematronVersion;
    }

    public function setDocumentVersion(float $documentVersion): void
    {
        $this->documentVersion = $documentVersion;
    }

    public function getMetadata(): ?Metadata
    {
        return $this->metadata;
    }

    public function setMetadata(?Metadata $metadata): void
    {
        $this->metadata = $metadata;
    }
}
