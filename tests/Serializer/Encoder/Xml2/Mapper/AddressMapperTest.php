<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Address;
use App\Serializer\Encoder\Xml2\Element\Email;
use App\Serializer\Encoder\Xml2\Element\Url;
use App\Serializer\Encoder\Xml2\Mapper\AddressMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AddressMapper
 */
class AddressMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Address::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Address::class]);
        $this->assertInstanceOf(AddressMapper::class, $service->classMap[Address::class][0]);

        $this->assertArrayHasKey(AddressMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[AddressMapper::elementName()]);
        $this->assertInstanceOf(AddressMapper::class, $service->elementMap[AddressMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Address $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<address %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Address();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Address $element, string $innerXml): void
    {
        $xml = sprintf('<address xmlns="%s" xmlns:m="%s">%s</address>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Address();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setAddressLine('address line');
        $innerXml .= '<add-line>address line</add-line>';
        yield 'add-line' => [deep_copy($element), $innerXml];

        $element->setOrganizationName('organization name');
        $innerXml .= '<orgname>organization name</orgname>';
        yield 'orgname' => [deep_copy($element), $innerXml];

        $element->setStreet('street');
        $innerXml .= '<street>street</street>';
        yield 'street' => [deep_copy($element), $innerXml];

        $element->setCity('city');
        $innerXml .= '<city>city</city>';
        yield 'city' => [deep_copy($element), $innerXml];

        $element->setState('state');
        $innerXml .= '<state>state</state>';
        yield 'state' => [deep_copy($element), $innerXml];

        $element->setPostalCode('postal code');
        $innerXml .= '<postalcode>postal code</postalcode>';
        yield 'postalcode' => [deep_copy($element), $innerXml];

        $element->setCountry('country');
        $innerXml .= '<country>country</country>';
        yield 'country' => [deep_copy($element), $innerXml];

        $element->setEmail(new Email());
        $innerXml .= '<email/>';
        yield 'email' => [deep_copy($element), $innerXml];

        $element->setUrl(new Url());
        $innerXml .= '<url/>';
        yield 'url' => [deep_copy($element), $innerXml];

        $element->setPhone('phone');
        $innerXml .= '<phone>phone</phone>';
        yield 'phone' => [deep_copy($element), $innerXml];

        $element->setFax('fax');
        $innerXml .= '<fax>fax</fax>';
        yield 'fax' => [deep_copy($element), $innerXml];
    }
}
