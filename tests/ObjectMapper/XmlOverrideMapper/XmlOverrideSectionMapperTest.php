<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideSectionMapper;

use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideSectionMapper */
class XmlOverrideSectionMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideSectionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideSectionMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Level|Section $element, CodeBook\Section $expected): void
    {
        $entity = new CodeBook\Section();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        // Base objects
        $element = new Section();
        $expected = new CodeBook\Section();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        //TitleGroup
        yield from $this->titleGroupCases($element, $expected);

        // Body
        $body = new SectionBody();
        $body->setBody('<p>Section content</p>');
        $element->setBody($body);
        $expected->setBody('<p>Section content</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        // Body with Level element (type union)
        $level = new Level();
        $bodyLevel = new SectionBody();
        $bodyLevel->setBody('<p>Level section body</p>');
        $level->setBody($bodyLevel);
        $expectedLevel = new CodeBook\Section();
        $expectedLevel->setUlid('uuid');
        $expectedLevel->setNodeId('nodeId');
        $expectedLevel->setBody('<p>Level section body</p>');
        yield 'level-body' => [deep_copy($level), deep_copy($expectedLevel)];
    }
}
