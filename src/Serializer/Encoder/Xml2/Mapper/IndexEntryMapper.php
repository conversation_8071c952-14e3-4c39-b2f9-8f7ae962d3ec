<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\TertiaryIndexEntry;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;

class IndexEntryMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'index-entry';
    }

    public static function dtoClass(): string
    {
        return IndexEntry::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof IndexEntry);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('primaryie' === $key && $value instanceof PrimaryIndexEntry) {
                $element->setPrimaryIndexEntry($value);
            } elseif ('secondaryie' === $key && $value instanceof SecondaryIndexEntry) {
                $children[] = $value;
            } elseif ('tertiaryie' === $key && $value instanceof TertiaryIndexEntry) {
                $children[] = $value;
            }
        }
        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof IndexEntry);

        // should be left unset
        unset($attrs['id']);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof IndexEntry);

        if ($element->getPrimaryIndexEntry()) {
            $writer->writeElement(self::CLARK . 'primaryie', $element->getPrimaryIndexEntry());
        }

        foreach ($element->getChildren() as $i) {
            if ($i instanceof SecondaryIndexEntry) {
                $writer->writeElement(self::CLARK . 'secondaryie', $i);
            } elseif ($i instanceof TertiaryIndexEntry) {
                $writer->writeElement(self::CLARK . 'tertiaryie', $i);
            }
        }
    }
}
