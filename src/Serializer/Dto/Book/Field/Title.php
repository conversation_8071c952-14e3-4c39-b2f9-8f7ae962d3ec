<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @since 1.11
 */
trait Title
{
    #[Groups(['list:contents'])]
    public string $title = '';
    public string $titleAbbreviation = '';
    public string $titleYear = '';

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getTitleAbbreviation(): string
    {
        return $this->titleAbbreviation;
    }

    public function setTitleAbbreviation(string $titleAbbreviation): void
    {
        $this->titleAbbreviation = $titleAbbreviation;
    }

    public function getTitleYear(): string
    {
        return $this->titleYear;
    }

    public function setTitleYear(string $titleYear): void
    {
        $this->titleYear = $titleYear;
    }
}
