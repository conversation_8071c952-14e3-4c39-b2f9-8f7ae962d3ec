<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookForewordMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookForewordMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookForewordMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookForewordMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Foreword $fromNode, CodeBook\Foreword $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Foreword();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Foreword();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setChildren([new BaseBook\Section(), new BaseBook\Section()]);
        $expected->setChildren([new CodeBook\Section(), new CodeBook\Section()]);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
