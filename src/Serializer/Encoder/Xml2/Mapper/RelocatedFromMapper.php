<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class Relocated<PERSON>romM<PERSON>per extends AbstractMapper
{
    public static function localName(): string
    {
        return 'relocated-from';
    }

    public static function dtoClass(): string
    {
        return RelocatedFrom::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof RelocatedFrom);

        if (array_key_exists('rid', $attrs) && is_string($attrs['rid'])) {
            $element->setReferenceId($attrs['rid']);
        }
        if (array_key_exists('relocated-from', $attrs) && is_string($attrs['relocated-from'])) {
            $element->setRelocatedFromAttr($attrs['relocated-from']);
        }
        if (array_key_exists('align', $attrs) && is_string($attrs['align'])) {
            $element->setAlign($attrs['align']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof RelocatedFrom);

        if (!empty($element->getReferenceId())) {
            $attrs['rid'] = $element->getReferenceId();
        }
        if (!empty($element->getRelocatedFromAttr())) {
            $attrs['relocated-from'] = $element->getRelocatedFromAttr();
        }
        if (!empty($element->getAlign()) && 'left' !== $element->getAlign()) {
            $attrs['align'] = $element->getAlign();
        }
    }
}
