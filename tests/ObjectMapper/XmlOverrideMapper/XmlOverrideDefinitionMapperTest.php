<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideDefinitionMapper;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\Term;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideDefinitionMapper */
class XmlOverrideDefinitionMapperTest extends KernelTestCase
{
    private ?XmlOverrideDefinitionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideDefinitionMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(DefinitionItem $element, CodeBook\Definition $expected): void
    {
        $entity = new CodeBook\Definition();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new DefinitionItem();
        $expected = new CodeBook\Definition();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // CommitteeDesignation
        $cd = new CommitteeDesignation();
        $cd->setBody('CD-123');
        $element->setCommitteeDesignation($cd);
        $expected->setCommitteeDesignation('CD-123');
        yield 'committeeDesignation' => [deep_copy($element), deep_copy($expected)];

        // Term
        $term = new Term();
        $term->setBody('Defined Term');
        $element->setTerm($term);
        $expected->setTerm('Defined Term');
        yield 'term' => [deep_copy($element), deep_copy($expected)];

        // Definition
        $definition = new Definition();
        $definition->setBody('The meaning of the term.');
        $element->setDefinition($definition);
        $expected->setDefinition('The meaning of the term.');
        yield 'definition' => [deep_copy($element), deep_copy($expected)];

        // All fields together
        $elementAll = new DefinitionItem();
        $cdAll = new CommitteeDesignation();
        $cdAll->setBody('CD-999');
        $termAll = new Term();
        $termAll->setBody('TermAll');
        $defAll = new Definition();
        $defAll->setBody('DefinitionAll');
        $elementAll->setCommitteeDesignation($cdAll);
        $elementAll->setTerm($termAll);
        $elementAll->setDefinition($defAll);

        $expectedAll = new CodeBook\Definition();
        $expectedAll->setUlid('uuid');
        $expectedAll->setNodeId('nodeId');
        $expectedAll->setCommitteeDesignation('CD-999');
        $expectedAll->setTerm('TermAll');
        $expectedAll->setDefinition('DefinitionAll');

        yield 'allFields' => [deep_copy($elementAll), deep_copy($expectedAll)];
    }
}
