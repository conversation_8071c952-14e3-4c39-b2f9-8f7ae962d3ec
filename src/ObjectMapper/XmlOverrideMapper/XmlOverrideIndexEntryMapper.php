<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverrideIndexEntryMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\IndexEntry $element, CodeBook\IndexEntry $entity): CodeBook\IndexEntry
    {
        if ($entry = $element->getPrimaryIndexEntry()) {
            $entity->setReferenceId($entry->getReferenceId());

            if ($term = $entry->getTerm()) {
                $entity->setTerm($term->getBody());
            }

            $entity->setNavPointerGroup($entry->getNavPointerGroup());
        }

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
