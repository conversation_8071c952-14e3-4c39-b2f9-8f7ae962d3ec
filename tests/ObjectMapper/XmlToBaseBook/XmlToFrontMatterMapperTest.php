<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToFrontMatterMapper;
use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToFrontMatterMapper */
class XmlToFrontMatterMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToFrontMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToFrontMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(FrontMatter $element, BaseBook\FrontMatter $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid($expected->getUlid());

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $actual->getTitlePage() && $actual->getTitlePage()->setUlid('uuid');
        $actual->getCopyrightPage() && $actual->getCopyrightPage()->setUlid('uuid');
        $actual->getPublisherNote() && $actual->getPublisherNote()->setUlid('uuid');
        $actual->getPreface() && $actual->getPreface()->setUlid('uuid');
        $actual->getForeword() && $actual->getForeword()->setUlid('uuid');

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new FrontMatter();
        $expected = new BaseBook\FrontMatter();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);

        $elementChildren = [];
        $expectedChildren = [];

        $titlePage = new TitlePage();
        $titlePage->setId('titlePage');
        $element->setTitlePage($titlePage);
        $elementChildren[] = $titlePage;

        $expectedTitlePage = new BaseBook\TitlePage();
        $expectedTitlePage->setUlid('uuid');
        $expectedTitlePage->setNodeId('titlePage');
        $expected->setTitlePage($expectedTitlePage);
        $expectedChildren[] = $expectedTitlePage;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'titlePage' => [deep_copy($element), deep_copy($expected)];

        $copyrightPage = new CopyrightPage();
        $copyrightPage->setId('copyright');
        $element->setCopyrightPage($copyrightPage);
        $elementChildren[] = $copyrightPage;

        $expectedCopyrightPage = new BaseBook\CopyrightPage();
        $expectedCopyrightPage->setUlid('uuid');
        $expectedCopyrightPage->setNodeId('copyright');
        $expected->setCopyrightPage($expectedCopyrightPage);
        $expectedChildren[] = $expectedCopyrightPage;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'copyrightPage' => [deep_copy($element), deep_copy($expected)];

        $publisherNote = new PublisherNote();
        $publisherNote->setId('pubNote');
        $element->setPublisherNote($publisherNote);
        $elementChildren[] = $publisherNote;

        $expectedPublisherNote = new BaseBook\PublisherNote();
        $expectedPublisherNote->setUlid('uuid');
        $expectedPublisherNote->setNodeId('pubNote');
        $expected->setPublisherNote($expectedPublisherNote);
        $expectedChildren[] = $expectedPublisherNote;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'publisherNote' => [deep_copy($element), deep_copy($expected)];

        $preface = new Preface();
        $preface->setId('preface');
        $element->setPreface($preface);
        $elementChildren[] = $preface;

        $expectedPreface = new BaseBook\Preface();
        $expectedPreface->setUlid('uuid');
        $expectedPreface->setNodeId('preface');
        $expected->setPreface($expectedPreface);
        $expectedChildren[] = $expectedPreface;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'preface' => [deep_copy($element), deep_copy($expected)];

        $foreword = new Foreword();
        $foreword->setId('foreword');
        $element->setForeword($foreword);
        $elementChildren[] = $foreword;

        $expectedForeword = new BaseBook\Foreword();
        $expectedForeword->setUlid('uuid');
        $expectedForeword->setNodeId('foreword');
        $expected->setForeword($expectedForeword);
        $expectedChildren[] = $expectedForeword;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'foreword' => [deep_copy($element), deep_copy($expected)];

        $section1 = new Section();
        $section1->setId('section1');
        $elementChildren[] = $section1;

        $expectedSection1 = new BaseBook\Section();
        $expectedSection1->setUlid('uuid');
        $expectedSection1->setNodeId('section1');
        $expectedChildren[] = $expectedSection1;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'section1' => [deep_copy($element), deep_copy($expected)];

        $section2 = new Section();
        $section2->setId('section2');
        $elementChildren[] = $section2;

        $expectedSection2 = new BaseBook\Section();
        $expectedSection2->setUlid('uuid');
        $expectedSection2->setNodeId('section2');
        $expectedChildren[] = $expectedSection2;

        $element->setChildren($elementChildren);
        $expected->setChildren($expectedChildren);

        yield 'section2' => [deep_copy($element), deep_copy($expected)];
    }
}
