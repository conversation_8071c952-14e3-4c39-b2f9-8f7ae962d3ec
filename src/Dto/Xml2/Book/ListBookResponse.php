<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Xml2\Book;

use App\Entity\Project;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

class ListBookResponse
{
    #[OA\Property(
        type: 'array',
        items: new OA\Items(ref: new Model(type: BookReference::class))
    )]
    public array $books = [];

    /**
     * @param Project[] $projects
     */
    public function __construct(array $projects = [])
    {
        foreach ($projects as $project) {
            $this->books[] = new BookReference($project);
        }
    }
}
