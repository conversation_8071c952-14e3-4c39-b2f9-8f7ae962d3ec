<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookPublicationMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookPublicationMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookPublicationMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookPublicationMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Publication $fromNode, CodeBook\Publication $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i)
        {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Publication();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Publication();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setChildren([new BaseBook\Volume(), new BaseBook\Volume()]);
        $expected->setChildren([new CodeBook\Volume(), new CodeBook\Volume()]);
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
