<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Field;

use App\Serializer\Encoder\Xml2\Element\QrCode;

trait QrCodeTrait
{
    public ?QrCode $qrCode = null;

    public function getQrCode(): ?QrCode
    {
        return $this->qrCode;
    }

    public function setQrCode(?QrCode $qrCode): void
    {
        $this->qrCode = $qrCode;
    }
}
