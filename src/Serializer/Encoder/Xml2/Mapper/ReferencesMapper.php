<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\References;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\repeatingElements;

/**
 * @since 1.11
 */
class ReferencesMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'references';
    }

    public static function dtoClass(): string
    {
        return References::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof References);
        $element->setChildren(repeatingElements($reader, self::CLARK . 'ref'));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof References);

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Reference) {
                $writer->writeElement(self::CLARK . 'ref', $i);
            }
        }
    }
}
