<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @since 1.16
 */
trait TitleGroup
{
    use CommitteeDesignation;
    use Label;
    use Number;
    use Title;

    #[Groups(['list:contents'])]
    public string $superTitle = '';
    #[Groups(['list:contents'])]
    public string $correlated = '';
    #[Groups(['list:contents'])]
    public string $subTitle = '';

    public function getSuperTitle(): string
    {
        return $this->superTitle;
    }

    public function setSuperTitle(string $superTitle): void
    {
        $this->superTitle = $superTitle;
    }

    public function getCorrelated(): string
    {
        return $this->correlated;
    }

    public function setCorrelated(string $correlated): void
    {
        $this->correlated = $correlated;
    }

    public function getSubTitle(): string
    {
        return $this->subTitle;
    }

    public function setSubTitle(string $subTitle): void
    {
        $this->subTitle = $subTitle;
    }
}
