<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Project;

use App\Entity\ProjectCategory;
use App\Serializer\Dto\UserDto;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Symfony\Component\Serializer\Annotation\Groups;

class ProjectCategoryDto
{
    #[OA\Property(type: 'integer', example: 123)]
    #[Groups(['getCategory', 'user:read'])]
    public ?int $id = null;

    #[OA\Property(type: 'string', example: 'Building Code')]
    #[Groups(['getCategory', 'user:read'])]
    public string $name = '';

    public array $projects = [];

    #[OA\Property(
        type: 'array',
        items: new OA\Items(ref: new Model(type: UserDto::class))
    )]
    public array $users = [];

    public static function create(ProjectCategory $category): self
    {
        $dto = new self();
        $dto->id   = $category->getId();
        $dto->name = $category->getName();

        foreach ($category->getProjects() as $project) {
            $data = [
                'id'                => $project->getId(),
                'shortCode'         => $project->getShortCode(),
                'baseBook'          => $project->getBaseBook(),
                'bookTitle'         => $project->getBookTitle(),
                'workingTitle'      => $project->getWorkingTitle(),
                'projectType'       => null,
                'versionType'       => null,
                'projectCategory'   => null,
                'createdDate'       => $project->getCreatedDate(),
                'lastModifiedDate'  => $project->getLastModifiedDate(),
                'active'            => $project->isActive(),
                'blueLine'          => $project->isBlueLine(),
                'commentaryEnabled' => $project->isCommentaryEnabled(),
                'collection'        => $project->getCollection(),
                'hasCdpAccess'      => $project->hasCdpAccess(),
                'cycle'             => null,
                'cdpAccessBookId'   => $project->getCdpAccessBookId(),
                'status'            => $project->getStatus(),
                'error_message'     => $project->getErrorMessage(),
                'manualImagePath'   => $project->getManualImagePath(),
                'users'             => [],
                'blueLineUser'      => $project->getBlueLineUser(),
                'blueLineDate'      => $project->getBlueLineDate(),
            ];

            if ($project->getProjectType()) {
                $data['projectType'] = [
                    'id'   => $project->getProjectType()->getId(),
                    'name' => $project->getProjectType()->getName(),
                ];
            }
            if ($project->getVersionType()) {
                $data['versionType'] = [
                    'id'   => $project->getVersionType()->getId(),
                    'name' => $project->getVersionType()->getName(),
                ];
            }
            if ($project->getProjectCategory()) {
                $data['projectCategory'] = [
                    'id'   => $project->getProjectCategory()->getId(),
                    'name' => $project->getProjectCategory()->getName(),
                ];
            }
            foreach ($project->getUsers() as $user) {
                $data['users'][] = ['id' => $user->getId()];
            }

            $dto->projects[] = $data;
        }

        foreach ($category->getUsers() as $user) {
            $dto->users[] = UserDto::create($user);
        }

        return $dto;
    }
}
