<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverrideChapterMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Level $element, CodeBook\Chapter $entity): CodeBook\Chapter
    {
        $this->mapTitleGroup($element, $entity);

        $entity->setDisplayLevel($element->getDisplayLevel());

        if ($history = $element->getHistory()) {
            $entity->setHistory($history->getBody());
        }

        if ($objectives = $element->getObjectives()) {
            $entity->setObjectives($objectives->getBody() ? $objectives->getBody()->getBody() : '');

            if ($objectives->getTitleGroup() && $title = $objectives->getTitleGroup()->getTitle()) {
                $entity->setObjectivesTitle($title->getBody());
            }
        }

        if ($abstract = $element->getAbstract()) {
            $entity->setAbstract($abstract->getBody());

            if ($abstract->getTitleGroup() && $title = $abstract->getTitleGroup()->getTitle()) {
                $entity->setAbstractTitle($title->getBody());
            }
        }

        if ($keywords = $element->getKeywords()) {
            $entity->setKeywords($keywords->getBody());

            if ($keywords->getTitleGroup() && $title = $keywords->getTitleGroup()->getTitle()) {
                $entity->setKeywordsTitle($title->getBody());
            }
        }

        if ($body = $element->getBody()) {
            $entity->setBody($body->getBody());
        }

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
