<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Exception\ApiException;
use App\Serializer\Encoder\Xml2\Element\Meta;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function get_class;
use function sprintf;

/**
 * @since 1.11
 */
class MetadataMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'metadata';
    }

    public static function dtoClass(): string
    {
        return Metadata::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Metadata);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('meta' === $key && $value instanceof Meta) {
                $children[] = $value;
            } else {
                throw new ApiException(sprintf('Unsupported metadata child "%s"', get_class($element)));
            }
        }
        $element->setChildren($children);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Metadata);

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Meta) {
                $localName = 'meta';
            } else {
                throw new ApiException(sprintf('Unsupported metadata child "%s"', get_class($element)));
            }

            $writer->writeElement(self::CLARK . $localName, $i);
        }
    }
}
