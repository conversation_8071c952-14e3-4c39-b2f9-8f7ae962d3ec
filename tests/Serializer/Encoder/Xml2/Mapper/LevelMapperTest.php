<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Acronym;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\LevelMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function range;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\LevelMapper
 */
class LevelMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Level::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Level::class]);
        $this->assertInstanceOf(LevelMapper::class, $service->classMap[Level::class][0]);

        foreach (range(1, 11) as $i) {
            $this->assertArrayHasKey(LevelMapper::elementName($i), $service->elementMap);
            $this->assertIsCallable($service->elementMap[LevelMapper::elementName($i)]);
            $this->assertInstanceOf(LevelMapper::class, $service->elementMap[LevelMapper::elementName($i)][0]);
        }
    }

    public function testDisplayLevels(): void
    {
        $xml = '';
        foreach (range(11, 2, -1) as $i) {
            $xml = sprintf('<level-%d>%s</level-%d>', $i, $xml, $i);
        }
        $xml = sprintf('<level-1 xmlns="%s" xmlns:m="%s">%s</level-1>', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $xml);

        $element = new Level();
        $element->setDisplayLevel(1);
        $current = $element;
        foreach (range(2, 11) as $i) {
            $newEl = new Level();
            $newEl->setDisplayLevel($i);
            $current->setChildren([$newEl]);
            $current = $newEl;
        }

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Level $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<level-1 %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Level();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setRole('chapter');
        $attrs['role'] = 'chapter';
        yield 'role' => [deep_copy($element), $attrs];

        $element->setIndexNumber('indexNumber');
        $attrs['indexnum'] = 'indexNumber';
        yield 'indexnum' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];

        $element->setTocAutoAdd(true);
        $attrs['tocautoadd'] = 'yes';
        yield 'tocautoadd' => [deep_copy($element), $attrs];

        $element->setReserveCount(100);
        $attrs['reservecount'] = '100';
        yield 'reservecount' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Level $element, string $innerXml): void
    {
        $xml = sprintf('<level-1 xmlns="%s" xmlns:m="%s">%s</level-1>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Level();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setMetadata(new Metadata());
        $innerXml .= '<metadata/>';
        yield 'metadata' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setBody(new SectionBody());
        $innerXml .= '<section-body/>';
        yield 'body' => [deep_copy($element), $innerXml];

        $child1 = new Level();
        $child1->setDisplayLevel(2);
        $element->setChildren([$child1]);
        $innerXml .= '<level-2/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $child2 = new Level();
        $child2->setDisplayLevel(2);
        $element->setChildren([$child1, $child2]);
        $innerXml .= '<level-2/>';
        yield '2 children' => [deep_copy($element), $innerXml];

        $child3 = new Section();
        $child3->setRole('section');
        $child3->setDisplayLevel(2);
        $element->setChildren([$child1, $child2, $child3]);
        $innerXml .= '<section disp-level="2"/>';
        yield 'section child' => [deep_copy($element), $innerXml];

        $child4 = new RelocatedTo();
        $element->setChildren([$child1, $child2, $child3, $child4]);
        $innerXml .= '<relocated-to/>';
        yield 'relocated-to child' => [deep_copy($element), $innerXml];

        $backMatter = new BackMatter();
        $element->setBackMatter($backMatter);
        $innerXml .= '<backmatter/>';
        yield 'backmatter' => [deep_copy($element), $innerXml];
    }

    public function testPromulgatorChapter(): void
    {
        $xml = <<<XML
<level-1 role="chapter" id="IWUIC2024V1.0_Ch07" tocautoadd="yes" indexnum="Ch 7" xmlns="https://schema.iccsafe.org/book/schema/1.0" xmlns:m="http://www.w3.org/1998/Math/MathML">
    <titlegroup>
        <label>Chapter</label>
        <number>7</number>
        <title>Referenced Standards</title>
    </titlegroup>
    <objectives>
        <titlegroup>
            <title>User note:</title>
        </titlegroup>
        <body>
            <note>
                <titlegroup>
                    <title-xml><emphasis type="bold">About this chapter:</emphasis></title-xml>
                </titlegroup>
                <p><emphasis>This code contains numerous references to standards promulgated by other organizations
                    that are used to provide requirements for materials and methods of construction. This chapter
                    contains a comprehensive list of all standards that are referenced in this code. These standards,
                    in essence, are part of this code to the extent of the reference to the standard.</emphasis></p>
                <p><emphasis>This chapter lists the standards that are referenced in various sections of this document.
                    The standards are listed herein by the promulgating agency of the standard, the standard
                    identification, the effective date and title, and the section or sections of this document that
                    reference the standard.</emphasis></p>
            </note>
        </body>
    </objectives>
    <section-body>
        <promulgator id="IWUIC2024V1.0_Ch07_PromASTM">
          <acronym>
            <url href="https://www.astm.org/">ASTM</url>
          </acronym>
        </promulgator>
        <promulgator id="IWUIC2024V1.0_Ch07_PromICC">
          <acronym>
            <url href="https://www.iccsafe.org/">ICC</url>
          </acronym>
        </promulgator>
        <promulgator id="IWUIC2024V1.0_Ch07_PromUL">
          <acronym>
            <url href="https://www.ul.com/">UL</url>
          </acronym>
        </promulgator>
    </section-body>
</level-1>
XML;

        $element = new Level();
        $element->setRole('chapter');
        $element->setId('IWUIC2024V1.0_Ch07');
        $element->setTocAutoAdd(true);
        $element->setIndexNumber('Ch 7');

        $titleGroup = new TitleGroup();
        $titleGroup->setLabel('Chapter');
        $titleGroup->setNumber('7');
        $titleGroup->setTitle('Referenced Standards');
        $element->setTitleGroup($titleGroup);

        $objectives = new Objectives();
        $objectives->setTitleGroup($tg = new TitleGroup());
        $tg->setTitle('User note:');
        $objectives->setBody(new Body(<<<XML
<note>
                <titlegroup>
                    <title-xml><emphasis type="bold">About this chapter:</emphasis></title-xml>
                </titlegroup>
                <p><emphasis>This code contains numerous references to standards promulgated by other organizations
                    that are used to provide requirements for materials and methods of construction. This chapter
                    contains a comprehensive list of all standards that are referenced in this code. These standards,
                    in essence, are part of this code to the extent of the reference to the standard.</emphasis></p>
                <p><emphasis>This chapter lists the standards that are referenced in various sections of this document.
                    The standards are listed herein by the promulgating agency of the standard, the standard
                    identification, the effective date and title, and the section or sections of this document that
                    reference the standard.</emphasis></p>
            </note>
XML));
        $element->setObjectives($objectives);

        $body = new SectionBody();
        $element->setBody($body);

        $promASTM = new Promulgator();
        $promASTM->setId('IWUIC2024V1.0_Ch07_PromASTM');
        $promASTM->setAcronym(new Acronym('<url href="https://www.astm.org/">ASTM</url>'));

        $promICC = new Promulgator();
        $promICC->setId('IWUIC2024V1.0_Ch07_PromICC');
        $promICC->setAcronym(new Acronym('<url href="https://www.iccsafe.org/">ICC</url>'));

        $promUL = new Promulgator();
        $promUL->setId('IWUIC2024V1.0_Ch07_PromUL');
        $promUL->setAcronym(new Acronym('<url href="https://www.ul.com/">UL</url>'));

        $body->setChildren([$promASTM, $promICC, $promUL]);
        $element->setChildren([$promASTM, $promICC, $promUL]);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
