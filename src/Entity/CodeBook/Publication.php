<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\CodeBook;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'code_book_publication')]
class Publication extends AbstractCodeBookNode
{
    #[ORM\OneToMany(mappedBy: 'publication', targetEntity: AbstractCodeBookNode::class, cascade: ['remove'])]
    private Collection $nodes;
    #[ORM\OneToMany(mappedBy: 'codeBook', targetEntity: XRefLink::class)]
    private Collection $allXRefLinks;
    #[ORM\OneToMany(mappedBy: 'codeBook', targetEntity: UrlLink::class)]
    private Collection $allUrlLinks;
    #[ORM\OneToMany(mappedBy: 'codeBook', targetEntity: PublicationRefLink::class)]
    private Collection $allPublicationRefLinks;

    public function __construct()
    {
        parent::__construct();
        $this->nodes = new ArrayCollection();
        $this->allXRefLinks = new ArrayCollection();
        $this->allUrlLinks = new ArrayCollection();
        $this->allPublicationRefLinks = new ArrayCollection();
    }

    public function getVolume(): ?Volume
    {
        foreach ($this->getChildren() as $child) {
            if ($child instanceof Volume) {
                return $child;
            }
        }

        return null;
    }

    public function getVolumes(): Collection
    {
        return (new ArrayCollection($this->getChildren()))
            ->filter(static fn ($child) => $child instanceof Volume);
    }

    public function getVolumeByNumber(string $nodeId): ?Volume
    {
        return $this->getVolumes()
            ->filter(static fn (Volume $v) => $v->getNodeId() == $nodeId)
            ->first() ?: null;
    }

    public function getNodes(): Collection
    {
        return $this->nodes;
    }

    public function getAllXRefLinks(): array
    {
        return $this->allXRefLinks->toArray();
    }

    public function getAllUrlLinks(): array
    {
        return $this->allUrlLinks->toArray();
    }

    public function getAllPublicationRefLinks(): array
    {
        return $this->allPublicationRefLinks->toArray();
    }
}
