<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Equation;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Xml2Schema;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function is_string;
use function preg_replace;
use function trim;

class EquationMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'equation';
    }

    public static function dtoClass(): string
    {
        return Equation::class;
    }

    /** @param Equation $element */
    protected function readAttributes($element, array $attrs): void
    {
        if (array_key_exists('tocentry', $attrs)) {
            $element->setTocEntry('yes' === $attrs['tocentry']);
        }
    }

    /** @param Equation $element */
    protected function readElement(Reader $reader, $element): void
    {
        $whereList = '';
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('label' === $key && $value instanceof Label) {
                $element->setLabel($value);
            } elseif ('number' === $key && $value instanceof Number) {
                $element->setNumber($value);
            } elseif (sprintf('{%s}math', Xml2Schema::XMLNS_M) === $key && is_string($value)) {
                $element->setMath($value);
            } elseif ('where-list' === $key && is_string($value)) {
                $whereList .= $value;
            }
        }

        $element->setWhereList($whereList);
    }

    /** @param Equation $element */
    protected function writeAttributes($element, array &$attrs): void
    {
        if ($element->hasTocEntry()) {
            $attrs['tocentry'] = 'yes';
        }
    }

    /** @param Equation $element */
    protected function writeElement(Writer $writer, $element): void
    {
        if ($element->getLabel()) {
            $writer->writeElement(self::CLARK . 'label', $element->getLabel());
        }
        if ($element->getNumber()) {
            $writer->writeElement(self::CLARK . 'number', $element->getNumber());
        }

        if (!empty($element->getMath())) {
            $math = trim($element->getMath());
            $math = preg_replace('/<semantics[^>]*>|<\/semantics>|<annotation[^>]*>.*?<\/annotation>/si', '', $math);
            $math = trim($math);
            $math = preg_replace([ '/<(\/?)([a-zA-Z][a-zA-Z0-9:-]*)([^>]*?)(\/?)>/'], ['<$1m:$2$3$4>'], $math);
            $writer->writeRaw('<m:math xmlns="http://www.w3.org/1998/Math/MathML">');
            $writer->writeRaw($math);
            $writer->writeRaw('</m:math>');
        }
        if (!empty($element->getWhereList())) {
            $whereList = trim($element->getWhereList());
            $writer->writeRaw('<where-list>');
            $writer->writeRaw($whereList);
            $writer->writeRaw('</where-list>');
        }
    }
}
