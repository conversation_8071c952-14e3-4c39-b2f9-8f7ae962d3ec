<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlDefinitionMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Definition $from): Xml2\DefinitionItem
    {
        $to = new Xml2\DefinitionItem();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);

        $to->setIndexNumber($from->getIndexNumber());

        if (!empty($from->getCommitteeDesignation())) {
            $cd = new Xml2\CommitteeDesignation();
            $cd->setBody($from->getCommitteeDesignation());
            $to->setCommitteeDesignation($cd);
        }
        if (!empty($from->getTerm())) {
            $term = new Xml2\Term();
            $term->setBody($from->getTerm());
            $to->setTerm($term);
        }
        if (!empty($from->getDefinition())) {
            $definition = new Xml2\Definition();
            $definition->setBody($from->getDefinition());
            $to->setDefinition($definition);
        }

        return $to;
    }
}
