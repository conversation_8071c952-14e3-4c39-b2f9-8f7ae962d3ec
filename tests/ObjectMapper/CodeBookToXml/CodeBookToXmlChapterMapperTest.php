<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlChapterMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlChapterMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlChapterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlChapterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Chapter $from, Xml2\Level $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Chapter();
        $from->setNodeId('');
        $from->setRole('');
        $expected = new Xml2\Level();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield 'indexNumber' => [deep_copy($from), deep_copy($expected)];

        $from->setReserveCount(100);
        $expected->setReserveCount(100);
        yield 'reserveCount' => [deep_copy($from), deep_copy($expected)];

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setTocAutoAdd(true);
        $expected->setTocAutoAdd(true);
        yield 'tocAutoAdd' => [deep_copy($from), deep_copy($expected)];

        $from->setDisplayLevel(100);
        $expected->setDisplayLevel(100);
        yield 'displayLevel' => [deep_copy($from), deep_copy($expected)];

        $from->setRelocatedFrom('relocatedFrom');
        $relocatedFrom = new Xml2\RelocatedFrom();
        $relocatedFrom->setBody('relocatedFrom');
        $expected->setRelocatedFrom($relocatedFrom);
        yield 'relocatedFrom' => [deep_copy($from), deep_copy($expected)];

        $from->setRelocatedFromId('relocatedFromId');
        $relocatedFrom->setReferenceId('relocatedFromId');
        yield 'relocatedFromId' => [deep_copy($from), deep_copy($expected)];

        $from->setHistory('history');
        $history = new Xml2\History();
        $history->setBody('history');
        $expected->setHistory($history);
        yield 'history' => [deep_copy($from), deep_copy($expected)];

        $from->setObjectives('objectives');
        $body = new Xml2\Body();
        $body->setBody('objectives');
        $objectives = new Xml2\Objectives();
        $objectives->setBody($body);
        $expected->setObjectives($objectives);
        yield 'objectives' => [deep_copy($from), deep_copy($expected)];

        $from->setObjectivesTitle('objectivesTitle');
        $title = new Xml2\Title();
        $title->setBody('objectivesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $objectives->setTitleGroup($titleGroup);
        yield 'objectivesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstract('abstract');
        $abstract = new Xml2\AbstractField();
        $abstract->setBody('abstract');
        $expected->setAbstract($abstract);
        yield 'abstract' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstractTitle('abstractTitle');
        $title = new Xml2\Title();
        $title->setBody('abstractTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $abstract->setTitleGroup($titleGroup);
        yield 'abstractTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywords('keywords');
        $keywords = new Xml2\Keywords();
        $keywords->setBody('keywords');
        $expected->setKeywords($keywords);
        yield 'keywords' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywordsTitle('keywordsTitle');
        $title = new Xml2\Title();
        $title->setBody('keywordsTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $keywords->setTitleGroup($titleGroup);
        yield 'keywordsTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setBody('body');
        $sectionBody = new Xml2\SectionBody();
        $sectionBody->setBody('body');
        $expected->setBody($sectionBody);
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $childSection = new CodeBook\Section();
        $childSection->setNodeId('childNode');
        $from->addChild($childSection);

        $expectedChild = new Xml2\Section();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
