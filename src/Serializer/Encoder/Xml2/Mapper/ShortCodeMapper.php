<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\ShortCode;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class ShortCodeMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'short-code';
    }

    public static function dtoClass(): string
    {
        return ShortCode::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof ShortCode);

        if (array_key_exists('short-url', $attrs) && is_string($attrs['short-url'])) {
            $element->setShortUrl($attrs['short-url']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof ShortCode);

        if (!empty($element->getShortUrl())) {
            $attrs['short-url'] = $element->getShortUrl();
        }
    }
}
