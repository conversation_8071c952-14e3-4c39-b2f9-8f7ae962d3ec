<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Section extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\IndexNumber;
    use Attribute\TocEntry;
    use Attribute\ReserveCount;
    use Attribute\DisplayLevel;
    use HasChildren;

    use Field\TitleGroupTrait;

    public ?SectionBody $body = null;
    public ?AbstractField $abstract = null;
    public ?Keywords $keywords = null;

    public function getBody(): ?SectionBody
    {
        return $this->body;
    }

    public function setBody(?SectionBody $body): void
    {
        $this->body = $body;
    }

    public function getAbstract(): ?AbstractField
    {
        return $this->abstract;
    }

    public function setAbstract(?AbstractField $abstract): void
    {
        $this->abstract = $abstract;
    }

    public function getKeywords(): ?Keywords
    {
        return $this->keywords;
    }

    public function setKeywords(?Keywords $keywords): void
    {
        $this->keywords = $keywords;
    }
}
