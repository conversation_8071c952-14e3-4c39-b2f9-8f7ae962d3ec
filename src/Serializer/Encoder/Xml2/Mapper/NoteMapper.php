<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function mb_trim;
use function preg_replace;
use function Sabre\Xml\Deserializer\keyValue;

/**
 * @since 1.11
 */
class NoteMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'note';
    }

    public static function dtoClass(): string
    {
        return Note::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Note);

        $innerXml = $reader->readInnerXml();
        $innerXml = preg_replace(self::REGEX_XMLNS, '', $innerXml);
        $innerXml = preg_replace(self::REGEX_TITLE_GROUP, '', $innerXml);
        $element->setBody(mb_trim($innerXml));

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
                break;
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Note);

        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());
    }
}
