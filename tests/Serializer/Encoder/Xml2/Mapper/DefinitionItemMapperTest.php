<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\DefinitionItemMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\DefinitionItemMapper
 */
class DefinitionItemMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(DefinitionItem::class, $service->classMap);
        $this->assertIsCallable($service->classMap[DefinitionItem::class]);
        $this->assertInstanceOf(DefinitionItemMapper::class, $service->classMap[DefinitionItem::class][0]);

        $this->assertArrayHasKey(DefinitionItemMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[DefinitionItemMapper::elementName()]);
        $this->assertInstanceOf(DefinitionItemMapper::class, $service->elementMap[DefinitionItemMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(DefinitionItem $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<def-item %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new DefinitionItem();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setIndexNumber('indexnum');
        $attrs['indexnum'] = 'indexnum';
        yield 'indexnum' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(DefinitionItem $element, string $innerXml): void
    {
        $xml = sprintf('<def-item xmlns="%s" xmlns:m="%s">%s</def-item>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new DefinitionItem();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->committeeDesignation = new CommitteeDesignation();
        $innerXml .= '<committee-desig/>';
        yield 'committee-desig' => [deep_copy($element), $innerXml];

        $element->setTerm(new Term());
        $innerXml .= '<term/>';
        yield 'term' => [deep_copy($element), $innerXml];

        $element->setDefinition(new Definition());
        $innerXml .= '<def/>';
        yield 'def' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(DefinitionItem $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $defXml = <<<XMLFRAG
<p>An establishment that sells prepared food for consumption.<phrase role="keyterm"> Restaurants</phrase> shall be classified as follows:</p>
<def-list>
    <def-item id="IZC2024V1.0_Ch02_Sec202_DefRESTAURANT_SubDefRESTAURANT_FAST_FOOD">
        <term>Restaurant, fast food. </term>
        <def><p>An establishment that sells food already prepared for consumption, packaged in paper, Styrofoam or similar materials, and may include drive-in or drive-up facilities for ordering.</p></def>
    </def-item>
    <def-item id="IZC2024V1.0_Ch02_Sec202_DefRESTAURANT_SubDefRESTAURANT_GENERAL">
        <term>Restaurant, general. </term>
        <def><p>An establishment that sells food for consumption on or off the premises.</p></def>
    </def-item>
    <def-item id="IZC2024V1.0_Ch02_Sec202_DefRESTAURANT_SubDefRESTAURANT_TAKE_OUT">
        <term>Restaurant, take out. </term>
        <def><p>An establishment that sells food only for consumption off the premises.</p></def>
    </def-item>
</def-list>
XMLFRAG;
        $xml = <<<XMLFRAG
<def-item %s id="IZC2024V1.0_Ch02_Sec202_DefRESTAURANT">
    <term>RESTAURANT.</term>
    <def>$defXml</def>
</def-item>
XMLFRAG;
        $term = new Term();
        $term->setBody('RESTAURANT.');
        $def = new Definition();
        $def->setBody($defXml);

        $element = new DefinitionItem();
        $element->setId('IZC2024V1.0_Ch02_Sec202_DefRESTAURANT');
        $element->setTerm($term);
        $element->setDefinition($def);
        yield 'IZC2024V1.0_Ch02_Sec202_DefRESTAURANT' => [$element, $xml];
    }
}
