<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookCopyrightPageMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\CopyrightPage $from): CodeBook\CopyrightPage
    {
        $to = new CodeBook\CopyrightPage();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        $to->setBody($from->getBody());

        return $to;
    }
}
