<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\UrlLink;
use App\Helper\Xml2Helper;
use App\Message\CodeBook\UpsertUrlLinksAsyncMessage;
use App\Service\StopwatchService;
use Doctrine\ORM\EntityManagerInterface;
use DOMElement;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Stopwatch\StopwatchEvent;

use function iterator_to_array;
use function mb_strpos;

#[AsMessageHandler]
class UpsertUrlLinksAsyncMessageHandler
{
    use ParseFieldsTrait;

    private StopwatchEvent $stopwatch;
    private bool $needsFlush = false;
    private bool $shouldFlush = true;

    public function __construct(
        private readonly EntityManagerInterface $em,
        StopwatchService                        $stopwatch,
    ) {
        $this->stopwatch = $stopwatch->create('UpsertUrlLinksAsyncMessage');
    }

    public function __invoke(UpsertUrlLinksAsyncMessage $message): void
    {

        $nodeId = $message->getNodeId();
        $node = $this->em->find(AbstractCodeBookNode::class, $nodeId);
        if (!$node) {
            return;
        }

        $this->stopwatch->start();

        $this->needsFlush = false;
        $this->parseFields($node, [$this, 'updateXml']);

        if ($this->needsFlush && $this->shouldFlush) {
            $this->em->flush();
        }

        $this->stopwatch->stop();
    }

    public function setAutoFlush(bool $autoFlush): void
    {
        $this->shouldFlush = $autoFlush;
    }

    public function __destruct()
    {
        if ($this->needsFlush && $this->shouldFlush) {
            $this->em->flush();
        }
    }

    protected function updateXml(string $xml, AbstractCodeBookNode $codeBookNode): string
    {
        if (false === mb_strpos($xml, '<url ')) {
            return $xml;
        }

        // <url href="https://codes.iccsafe.org/codes/i-codes">codes.iccsafe.org/codes/i-codes</url>
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $links = $xpath->query('//x:url', $document->documentElement);
        if ($links->length === 0) {
            return $xml;
        }

        $existing = [];
        foreach ($codeBookNode->getUrlLinks() as $i) {
            $existing[$i->getUuid()] = $i;
        }

        /** @var DOMElement[] $linksArr */
        $linksArr = iterator_to_array($links);
        foreach ($linksArr as $link) {
            $uuid = $link->getAttribute('ct-uuid');
            $href = $link->getAttribute('href');
            $alt = $link->getAttribute('alt');
            $target = $link->getAttribute('target');
            $body = Xml2Helper::getInnerXml($link);

            if (!empty($uuid) && isset($existing[$uuid])) {
                $urlLink = $existing[$uuid];
                unset($existing[$uuid]);
            } else {
                $urlLink = new UrlLink();
                $urlLink->setCodeBook($codeBookNode->getPublication());
                $link->setAttribute('ct-uuid', $urlLink->getUuid());

                $this->needsFlush = true;
            }

            $urlLink->setHref($href);
            $urlLink->setAlt($alt);
            $urlLink->setTarget($target);
            $urlLink->setBody($body);

            $codeBookNode->addUrlLink($urlLink);
            $this->em->persist($urlLink);
        }

        foreach ($existing as $i) {
            $this->em->remove($i);
            $this->needsFlush = true;
        }

        return Xml2Helper::getInnerXml($document->documentElement);
    }
}
