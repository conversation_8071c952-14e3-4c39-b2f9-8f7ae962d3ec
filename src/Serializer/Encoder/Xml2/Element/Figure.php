<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Figure extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\TocEntry;
    use Attribute\FloatAttr;
    use Attribute\Orientation;

    use Field\TitleGroupTrait;

    public string $media = '';
    public ?Caption $caption = null;
    public ?FigureNotes $figureNotes = null;
    public string $legend = '';
    public ?Source $source = null;
    public ?Credit $credit = null;

    public function getMedia(): string
    {
        return $this->media;
    }

    public function setMedia(string $media): void
    {
        $this->media = $media;
    }

    public function getCaption(): ?Caption
    {
        return $this->caption;
    }

    public function setCaption(?Caption $caption): void
    {
        $this->caption = $caption;
    }

    public function getFigureNotes(): ?FigureNotes
    {
        return $this->figureNotes;
    }

    public function setFigureNotes(?FigureNotes $figureNotes): void
    {
        $this->figureNotes = $figureNotes;
    }

    public function getLegend(): string
    {
        return $this->legend;
    }

    public function setLegend(string $legend): void
    {
        $this->legend = $legend;
    }

    public function getSource(): ?Source
    {
        return $this->source;
    }

    public function setSource(?Source $source): void
    {
        $this->source = $source;
    }

    public function getCredit(): ?Credit
    {
        return $this->credit;
    }

    public function setCredit(?Credit $credit): void
    {
        $this->credit = $credit;
    }
}
