<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

use function is_string;

/**
 * @since 1.16
 */
class TitleGroup extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;

    public ?SuperTitle $superTitle = null;
    public ?CommitteeDesignation $committeeDesignation = null;
    public ?Label $label = null;
    public ?Number $number = null;
    public ?Correlated $correlated = null;
    public ?Title $title = null;
    public ?SubTitle $subTitle = null;
    public ?History $history = null;

    public function getSuperTitle(): ?SuperTitle
    {
        return $this->superTitle;
    }

    public function setSuperTitle(SuperTitle|string|null $value): void
    {
        $this->superTitle = is_string($value)
            ? new SuperTitle($value)
            : $value;
    }

    public function getCommitteeDesignation(): ?CommitteeDesignation
    {
        return $this->committeeDesignation;
    }

    public function setCommitteeDesignation(CommitteeDesignation|string|null $value): void
    {
        $this->committeeDesignation = is_string($value)
            ? new CommitteeDesignation($value)
            : $value;
    }

    public function getLabel(): ?Label
    {
        return $this->label;
    }

    public function setLabel(Label|string|null $value): void
    {
        $this->label = is_string($value)
            ? new Label($value)
            : $value;
    }

    public function getNumber(): ?Number
    {
        return $this->number;
    }

    public function setNumber(Number|string|null $value): void
    {
        $this->number = is_string($value)
            ? new Number($value)
            : $value;
    }

    public function getCorrelated(): ?Correlated
    {
        return $this->correlated;
    }

    public function setCorrelated(?Correlated $correlated): void
    {
        $this->correlated = $correlated;
    }

    public function getTitle(): ?Title
    {
        return $this->title;
    }

    public function setTitle(Title|string|null $value): void
    {
        $this->title = is_string($value)
            ? new Title($value)
            : $value;
    }

    public function getSubTitle(): ?SubTitle
    {
        return $this->subTitle;
    }

    public function setSubTitle(SubTitle|string|null $value): void
    {
        $this->subTitle = is_string($value)
            ? new SubTitle($value)
            : $value;
    }

    public function getHistory(): ?History
    {
        return $this->history;
    }

    public function setHistory(?History $history): void
    {
        $this->history = $history;
    }
}
