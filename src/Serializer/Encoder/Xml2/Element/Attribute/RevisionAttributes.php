<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use App\Helper\DateTimeHelper;
use DateTime;

/**
 * @since 1.11
 */
trait RevisionAttributes
{
    public string $revisionBy = '';
    public ?DateTime $revisionDateTime = null;
    public string $revision = '';
    public string $revisionGroup = '';
    public string $dataChanged = '';
    public string $dataChangedIn = '';
    public string $relocatedFromAttr = '';

    public function getRevisionBy(): string
    {
        return $this->revisionBy;
    }

    public function setRevisionBy(string $revisionBy): void
    {
        $this->revisionBy = $revisionBy;
    }

    public function getRevisionDateTime(): ?DateTime
    {
        return $this->revisionDateTime;
    }

    public function setRevisionDateTime(DateTime|string|int|null $revisionDateTime): void
    {
        $this->revisionDateTime = DateTimeHelper::parseToDateTime($revisionDateTime);
    }

    public function getRevision(): string
    {
        return $this->revision;
    }

    public function setRevision(string $revision): void
    {
        $this->revision = $revision;
    }

    public function getRevisionGroup(): string
    {
        return $this->revisionGroup;
    }

    public function setRevisionGroup(string $revisionGroup): void
    {
        $this->revisionGroup = $revisionGroup;
    }

    public function getDataChanged(): string
    {
        return $this->dataChanged;
    }

    public function setDataChanged(string $dataChanged): void
    {
        $this->dataChanged = $dataChanged;
    }

    public function getDataChangedIn(): string
    {
        return $this->dataChangedIn;
    }

    public function setDataChangedIn(string $dataChangedIn): void
    {
        $this->dataChangedIn = $dataChangedIn;
    }

    public function getRelocatedFromAttr(): string
    {
        return $this->relocatedFromAttr;
    }

    public function setRelocatedFromAttr(string $relocatedFromAttr): void
    {
        $this->relocatedFromAttr = $relocatedFromAttr;
    }
}
