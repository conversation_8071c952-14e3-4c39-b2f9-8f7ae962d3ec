# Development environment override
services:
  php:
    container_name: ${IMAGES_PREFIX:-}ct-api-local
    build:
      context: .
      target: frankenphp_dev
    volumes:
      - ./:/app
      - ./frankenphp/Caddyfile:/etc/frankenphp/Caddyfile:ro
      - ./frankenphp/conf.d/20-app.dev.ini:/usr/local/etc/php/app.conf.d/20-app.dev.ini:ro

      # supervisord
      - ./frankenphp/supervisord.conf:/etc/supervisor/supervisord.conf:ro
      - ./frankenphp/supervisord-app.conf:/etc/supervisor/conf.d/supervisord-app.conf:ro

      # If you develop on Mac or Windows you can remove the vendor/ directory
      #  from the bind-mount for better performance by enabling the next line:
      #- /app/vendor
    environment:
      APP_ENV: "${APP_ENV:-dev}"

      FRANKENPHP_WORKER_CONFIG: watch
      MERCURE_EXTRA_DIRECTIVES: demo
      # See https://xdebug.org/docs/all_settings#mode
      XDEBUG_MODE: "${XDEBUG_MODE:-off}"
    extra_hosts:
      # Ensure that host.docker.internal is correctly defined on Linux
      - host.docker.internal:${LOCAL_IP:-host-gateway}
    tty: true

  run-phpunit:
    build:
      context: .
      target: frankenphp_dev
      args:
        PHP_VERSION: "${PHP_VERSION:-8.2}"
    command: [ 'php', 'vendor/bin/phpunit' ]
    volumes:
      - ./:/app
      - ./.composer-cache/:/composer-cache/
    environment:
      APP_ENV: "${APP_ENV:-test}"
      COMPOSER_CACHE_DIR: '/composer-cache'
    tty: true

###> symfony/mercure-bundle ###
###< symfony/mercure-bundle ###

###> doctrine/doctrine-bundle ###
#  database:
#    ports:
#      - "8306:3306"
###< doctrine/doctrine-bundle ###
