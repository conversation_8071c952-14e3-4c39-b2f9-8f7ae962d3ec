<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookDefinitionMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookDefinitionMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookDefinitionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookDefinitionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Definition $fromNode, CodeBook\Definition $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Definition();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Definition();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        $expected->setRole('');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield 'indexNumber' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCommitteeDesignation('committeeDesignation');
        $expected->setCommitteeDesignation('committeeDesignation');
        yield 'termCommitteeDesignation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTerm('term');
        $expected->setTerm('term');
        yield 'term' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDefinition('definition');
        $expected->setDefinition('definition');
        yield 'definition' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
