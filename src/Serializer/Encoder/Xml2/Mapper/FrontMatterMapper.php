<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;

/**
 * @since 1.16
 */
class FrontMatterMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'frontmatter';
    }

    public static function dtoClass(): string
    {
        return FrontMatter::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof FrontMatter);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlepage' === $key && $value instanceof TitlePage) {
                $element->setTitlePage($value);
            } elseif ('copyright-page' === $key && $value instanceof CopyrightPage) {
                $element->setCopyrightPage($value);
            } elseif ('publisher-note' === $key && $value instanceof PublisherNote) {
                $element->setPublisherNote($value);
            } elseif ('foreword' === $key && $value instanceof Foreword) {
                $element->setForeword($value);
            } elseif ('preface' === $key && $value instanceof Preface) {
                $element->setPreface($value);
            } elseif ('section' === $key && $value instanceof Section) {
                $children[] = $value;
            }
        }

        $element->setChildren($children);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof FrontMatter);

        $this->writeElements($writer, [
            'titlepage'      => $element->getTitlePage(),
            'copyright-page' => $element->getCopyrightPage(),
            'publisher-note' => $element->getPublisherNote(),
            'foreword'       => $element->getForeword(),
            'preface'        => $element->getPreface(),
        ]);

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Section) {
                $i->setDisplayLevel(1);
                $writer->writeElement(self::clark('section'), $i);
            }
        }
    }
}
