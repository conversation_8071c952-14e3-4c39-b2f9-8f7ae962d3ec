<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

use function settype;

/**
 * @since 1.11
 */
trait ReserveCount
{
    public int $reserveCount = 0;

    public function getReserveCount(): int
    {
        return $this->reserveCount;
    }

    /** @param string|int $reserveCount */
    public function setReserveCount($reserveCount): void
    {
        if (settype($reserveCount, 'integer')) {
            $this->reserveCount = (int) $reserveCount;
        }
    }
}
