<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlToFrontMatterMapper extends AbstractXmlToBaseBookMapper
{
    public function map(Element\FrontMatter $element): BaseBook\FrontMatter
    {
        $entity = new BaseBook\FrontMatter();
        $this->mapCommonAttributes($element, $entity);

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            $entity->setChildren($children);

            foreach ($children as $i) {
                if ($i instanceof BaseBook\TitlePage) {
                    $entity->setTitlePage($i);
                } elseif ($i instanceof BaseBook\CopyrightPage) {
                    $entity->setCopyrightPage($i);
                } elseif ($i instanceof BaseBook\PublisherNote) {
                    $entity->setPublisherNote($i);
                } elseif ($i instanceof BaseBook\Preface) {
                    $entity->setPreface($i);
                } elseif ($i instanceof BaseBook\Foreword) {
                    $entity->setForeword($i);
                }
            }
        }

        return $entity;
    }
}
