<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\QrCodePurpose;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\ShortCode;
use App\Serializer\Encoder\Xml2\Mapper\QrCodeMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\QrCodeMapper
 */
class QrCodeMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(QrCode::class, $service->classMap);
        $this->assertIsCallable($service->classMap[QrCode::class]);
        $this->assertInstanceOf(QrCodeMapper::class, $service->classMap[QrCode::class][0]);

        $this->assertArrayHasKey(QrCodeMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[QrCodeMapper::elementName()]);
        $this->assertInstanceOf(QrCodeMapper::class, $service->elementMap[QrCodeMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(QrCode $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<QR-code %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new QrCode();
        $attrs = [
            'display'  => 'no',
            'levelref' => '',
            'purpose'  => '',
        ];
        yield 'baseline' => [deep_copy($element), $attrs];

        $element->setId('id');
        $attrs['id'] = 'id';
        yield 'id' => [deep_copy($element), $attrs];

        $element->setQrDisplay(true);
        $attrs['display'] = 'yes';
        yield 'display' => [deep_copy($element), $attrs];

        $element->setLevelReference('levelReference');
        $attrs['levelref'] = 'levelReference';
        yield 'levelref' => [deep_copy($element), $attrs];

        $element->setPurpose(QrCodePurpose::ADD);
        $attrs['purpose'] = QrCodePurpose::ADD;
        yield 'purpose' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(QrCode $element, string $innerXml): void
    {
        $xml = sprintf('<QR-code xmlns="%s" xmlns:m="%s" display="no" levelref="" purpose="">%s</QR-code>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new QrCode();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setImg('<img src="" alt=""/>');
        $innerXml .= '<img src="" alt=""/>';
        yield 'img' => [deep_copy($element), $innerXml];

        $element->setShortCode(new ShortCode());
        $innerXml .= '<short-code/>';
        yield 'short-code' => [deep_copy($element), $innerXml];

        $element->setBookIcon('<book-icon/>');
        $innerXml .= '<book-icon/>';
        yield 'book-icon' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(QrCode $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $xml = <<<XMLFRAG
<QR-code id="qr-IBC2024V2.0_AppxG_SecG109" display="yes" levelref="section" purpose="change" %s>
    <img src="Images/IBC2024V2.0_AppxG_SecG109_printable-47fac27.png" alt="QR-Code"/>
    <short-code short-url="1c245fe"/>
</QR-code>
XMLFRAG;
        $element = new QrCode();
        $element->setId('qr-IBC2024V2.0_AppxG_SecG109');
        $element->setQrDisplay(true);
        $element->setLevelReference('section');
        $element->setPurpose(QrCodePurpose::CHANGE);
        $element->setImg('<img src="Images/IBC2024V2.0_AppxG_SecG109_printable-47fac27.png" alt="QR-Code"/>');

        $shortCode = new ShortCode();
        $shortCode->setShortUrl('1c245fe');
        $element->setShortCode($shortCode);
        yield 'qr-IBC2024V2.0_AppxG_SecG109' => [$element, $xml];

        $xml = <<<XMLFRAG
<QR-code id="qr-IMC2024V2.0_Ch01_Sec104" display="yes" levelref="section" purpose="change" %s>
    <img src="Images/IMC2024V2.0_Ch01_Sec104_printable-e2410eb.png" alt="QR-Code"/>
    <short-code short-url="e2410eb"/>
    <book-icon src="Images/23-22525_PROD_I-Code_QR_Code_ICON_FINAL_IMC.png" alt="QR-Code"/>
</QR-code>
XMLFRAG;
        $element = new QrCode();
        $element->setId('qr-IMC2024V2.0_Ch01_Sec104');
        $element->setQrDisplay(true);
        $element->setLevelReference('section');
        $element->setPurpose(QrCodePurpose::CHANGE);
        $element->setImg('<img src="Images/IMC2024V2.0_Ch01_Sec104_printable-e2410eb.png" alt="QR-Code"/>');
        $element->setBookIcon('<book-icon src="Images/23-22525_PROD_I-Code_QR_Code_ICON_FINAL_IMC.png" alt="QR-Code"/>');

        $shortCode = new ShortCode();
        $shortCode->setShortUrl('e2410eb');
        $element->setShortCode($shortCode);
        yield 'qr-IMC2024V2.0_Ch01_Sec104' => [$element, $xml];
    }
}
