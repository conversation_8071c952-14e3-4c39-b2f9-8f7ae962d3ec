<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\TableMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TableMapper
 */
class TableMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Table::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Table::class]);
        $this->assertInstanceOf(TableMapper::class, $service->classMap[Table::class][0]);

        $this->assertArrayHasKey(TableMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TableMapper::elementName()]);
        $this->assertInstanceOf(TableMapper::class, $service->elementMap[TableMapper::elementName()][0]);
    }

    /**
     * @dataProvider attributeCases
     * @dataProvider calsAttributeCases
     * @dataProvider htmlAttributeCases
     */
    public function testAttributes(Table $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<formal-table %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        $this->commonAttributeCases($element, $attrs);
        $this->revisionAttributeCases($element, $attrs);
    }

    public function calsAttributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'cals / baseline' => [deep_copy($element), $attrs];

        $element->setFrame(Frame::SIDES);
        $attrs['frame'] = Frame::SIDES;
        yield 'cals / frame' => [deep_copy($element), $attrs];

        $element->setOrientation(Orientation::LANDSCAPE);
        $attrs['orient'] = Orientation::LANDSCAPE;
        yield 'cals / orient' => [deep_copy($element), $attrs];

        $element->setColumnSeparator(true);
        $attrs['colsep'] = '1';
        yield 'cals / colsep' => [deep_copy($element), $attrs];

        $element->setRowSeparator(true);
        $attrs['rowsep'] = '1';
        yield 'cals / rowsep' => [deep_copy($element), $attrs];

        $element->setFloat(FloatEnum::TOP);
        $attrs['float'] = FloatEnum::TOP;
        yield 'cals / float' => [deep_copy($element), $attrs];

        $element->setBackgroundColor('#fff');
        $attrs['background-color'] = '#fff';
        yield 'cals / background-color' => [deep_copy($element), $attrs];

        $element->setTableStyle('tabstyle');
        $attrs['tabstyle'] = 'tabstyle';
        yield 'cals / tabstyle' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'cals / tocentry = yes' => [deep_copy($element), $attrs];

        $element->setPageWide(true);
        $attrs['pgwide'] = 'yes';
        yield 'cals / pgwide = yes' => [deep_copy($element), $attrs];
    }

    public function htmlAttributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'html / baseline' => [deep_copy($element), $attrs];

        $element->setFrame(Frame::LEFT_HAND_SIDE);
        $attrs['frame'] = Frame::LEFT_HAND_SIDE;
        yield 'html / frame' => [deep_copy($element), $attrs];

        $element->setClass('class');
        $attrs['class'] = 'class';
        yield 'html / class' => [deep_copy($element), $attrs];

        $element->setTitleAttr('title');
        $attrs['title'] = 'title';
        yield 'html / title' => [deep_copy($element), $attrs];

        $element->setSummary('summary');
        $attrs['summary'] = 'summary';
        yield 'html / summary' => [deep_copy($element), $attrs];

        $element->setWidth('123');
        $attrs['width'] = '123';
        yield 'html / width' => [deep_copy($element), $attrs];

        $element->setBorder('123');
        $attrs['border'] = '123';
        yield 'html / border' => [deep_copy($element), $attrs];

        $element->setCellSpacing(123);
        $attrs['cellspacing'] = '123';
        yield 'html / cellspacing' => [deep_copy($element), $attrs];

        $element->setCellPadding(123);
        $attrs['cellpadding'] = '123';
        yield 'html / cellpadding' => [deep_copy($element), $attrs];

        $element->setRules(Rules::ALL);
        $attrs['rules'] = Rules::ALL;
        yield 'html / rules' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Table $element, string $innerXml): void
    {
        $xml = sprintf('<formal-table xmlns="%s" xmlns:m="%s">%s</formal-table>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Table();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setQrCode(new QrCode());
        $innerXml .= '<QR-code display="no" levelref="" purpose=""/>';
        yield 'QR-code' => [deep_copy($element), $innerXml];

        $element->setBody('<table/>');
        $innerXml .= '<table/>';
        yield 'table' => [deep_copy($element), $innerXml];

        $element->setCaption(new Caption());
        $innerXml .= '<caption/>';
        yield 'caption' => [deep_copy($element), $innerXml];

        $element->setLegend('<legend/>');
        $innerXml .= '<legend/>';
        yield 'legend' => [deep_copy($element), $innerXml];

        $element->setTableNotes(new TableNotes());
        $innerXml .= '<table-notes/>';
        yield 'table-notes' => [deep_copy($element), $innerXml];

        $element->setSource(new Source());
        $innerXml .= '<source/>';
        yield 'source' => [deep_copy($element), $innerXml];

        $innerXml .= '<credit/>';
        $element->setCredit(new Credit());
        yield 'credit' => [deep_copy($element), $innerXml];
    }
}
