<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Enum\Align;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookRelocatedFromMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookRelocatedFromMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookRelocatedFromMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookRelocatedFromMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\RelocatedFrom $fromNode, CodeBook\RelocatedFrom $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\RelocatedFrom();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\RelocatedFrom();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setIndent(true);
        $expected->setIndent(true);
        yield 'indent' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setAlign(Align::CENTER);
        $expected->setAlign(Align::CENTER);
        yield 'align' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setReferenceId('referenceId');
        $expected->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
