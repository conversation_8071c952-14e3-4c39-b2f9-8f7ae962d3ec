<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Preface;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\RelocatedFrom;
use App\Entity\CodeBook\RelocatedTo;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\CodeBook\TitlePage;
use App\Entity\CodeBook\Volume;
use App\Entity\CodeBook\XRefLink;
use App\Helper\Xml2Helper;
use App\Message\CodeBook\UpsertXRefLinksAsyncMessage;
use App\Service\StopwatchService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use DOMElement;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

use Symfony\Component\Stopwatch\StopwatchEvent;

use function iterator_to_array;
use function mb_strlen;
use function mb_strpos;
use function mb_substr;
use function mb_trim;
use function preg_match;
use function preg_replace;

use const XML_TEXT_NODE;

#[AsMessageHandler]
class UpsertXRefLinksAsyncMessageHandler
{
    use ParseFieldsTrait;

    private EntityRepository $xrefRepo;
    private EntityRepository $nodeRepo;
    private StopwatchEvent $stopwatch;
    private bool $needsFlush = false;
    private bool $shouldFlush = true;

    public function __construct(
        private readonly EntityManagerInterface $em,
        StopwatchService                        $stopwatch,
    ) {
        $this->xrefRepo = $em->getRepository(XRefLink::class);
        $this->nodeRepo = $em->getRepository(AbstractCodeBookNode::class);
        $this->stopwatch = $stopwatch->create('UpsertXRefLinksMessage');
    }

    public function __invoke(UpsertXRefLinksAsyncMessage $message): void
    {

        $nodeId = $message->getNodeId();
        /** @var AbstractCodeBookNode|null $node */
        $node = $this->em->find(AbstractCodeBookNode::class, $nodeId);
        if (!$node) {
            return;
        }

        $this->stopwatch->start();
        $this->needsFlush = false;

        $this->parseFields($node, [$this, 'updateXml']);

        if ($this->needsFlush && $this->shouldFlush) {
            $this->em->flush();
        }

        $this->needsFlush = false;
        $this->stopwatch->stop();
    }

    public function setAutoFlush(bool $autoFlush): void
    {
        $this->shouldFlush = $autoFlush;
    }

    protected function updateXml(string $xml, AbstractCodeBookNode $codeBookNode): string
    {
        if (mb_strpos($xml, '<xref ') === false) return $xml;

        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath    = Xml2Helper::createDOMXPath($document);

        $links = $xpath->query('//x:xref', $document->documentElement);
        if (!$links || $links->length === 0) return $xml;

        $existing = [];
        foreach ($codeBookNode->getXRefLinks() as $e) {
            $existing[$e->getUuid()] = $e;
        }

        /** @var DOMElement[] $linksArr */
        $linksArr = iterator_to_array($links);
        foreach ($linksArr as $link) {
            $uuid = (string) $link->getAttribute('ct-uuid');

            if ($uuid !== '' && isset($existing[$uuid])) {
                /** @var XRefLink $entity */
                $entity = $existing[$uuid];

                if ($entity->getFromNode() !== $codeBookNode) {
                    $entity->setFromNode($codeBookNode);
                    $this->needsFlush = true;
                }

                // rid -> toNode
                $rid = $link->getAttribute('rid') ?: '';
                $entity->setToNodeUuid($rid);

                $toNode = $this->getToNode($rid, $codeBookNode->getPublication());

                if ($toNode) {
                    $entity->setToNode($toNode);
                    $entity->setValidRid(true);
                    // normalize rid in DOM to ULID
                    $link->setAttribute('rid', $toNode->getUlid());
                } else {
                    $entity->setToNode(null);
                    $entity->setValidRid(false);
                }

                $this->parseBody($entity, $link);
                $this->parseParts($entity);

                $this->needsFlush = true;

                unset($existing[$uuid]);
                continue;
            }

            $this->createXref($codeBookNode, $link);
            $this->needsFlush = true;
        }

        foreach ($existing as $orphan) {
            $this->em->remove($orphan);
            $this->needsFlush = true;
        }

        return Xml2Helper::getInnerXml($document->documentElement);
    }

    protected function createXref(AbstractCodeBookNode $codeBookNode, DOMElement $link): XRefLink
    {
        $rid = $link->getAttribute('rid') ?: '';

        $entity = new XRefLink();
        $entity->setCodeBook($codeBookNode->getPublication());
        $entity->setFromNode($codeBookNode);
        $entity->setToNodeUuid($rid);

        $toNode = $this->getToNode($rid, $codeBookNode->getPublication());
        if ($toNode) {
            $entity->setToNode($toNode);
            $entity->setValidRid(true);
            $link->setAttribute('rid', $toNode->getUlid());
        } else {
            $entity->setToNode(null);
            $entity->setValidRid(false);
        }

        $this->parseBody($entity, $link);
        $this->parseParts($entity);

        $this->em->persist($entity);

        $link->setAttribute('ct-uuid', $entity->getUuid());

        return $entity;
    }

    protected function parseBody(XRefLink $xref, DOMElement $link): void
    {
        // changes: <p><xref> <emphasis> link </emphasis> </xref></p>
        // to: <p> <xref><emphasis> link </emphasis></xref> </p>
        $this->moveDOMWhitespace($link);

        // changes: <p> <emphasis><xref> link </xref></emphasis> </p>
        // to: <p> <xref><emphasis> link </emphasis></xref> </p>
        $this->swapXrefEmphasis($link);

        // changes: <p> <emphasis><xref> link </xref></emphasis> </p>
        // to: <p> <emphasis> <xref>link</xref> </emphasis> </p>
        $this->moveDOMWhitespace($link);

        // read body and normalize spaces.
        $body = Xml2Helper::getInnerXml($link);
        $body = preg_replace('/\s+/', ' ', $body);

        $xref->setBody($body);
        $xref->setOriginalBody($body);
    }

    protected function moveDOMWhitespace(DOMElement $xref): void
    {
        $document = $xref->ownerDocument;
        $parent = $xref->parentNode;

        if (!$parent) {
            return;
        }

        $firstChild = $xref->firstChild;
        if ($firstChild && $firstChild->nodeType === XML_TEXT_NODE) {
            $text = $firstChild->nodeValue;
            if (preg_match('/^(\s+)/', $text, $matches)) {
                $whitespace = $matches[1];

                $whitespaceNode = $document->createTextNode($whitespace);
                $parent->insertBefore($whitespaceNode, $xref);

                $remainingText = mb_substr($text, mb_strlen($whitespace));
                if (empty($remainingText)) {
                    $xref->removeChild($firstChild);
                } else {
                    $firstChild->nodeValue = $remainingText;
                }
            }
        }

        $lastChild = $xref->lastChild;
        if ($lastChild && $lastChild->nodeType === XML_TEXT_NODE) {
            $text = $lastChild->nodeValue;
            if (preg_match('/(\s+)$/', $text, $matches)) {
                $whitespace = $matches[1];

                $whitespaceNode = $document->createTextNode($whitespace);
                if ($xref->nextSibling) {
                    $parent->insertBefore($whitespaceNode, $xref->nextSibling);
                } else {
                    $parent->appendChild($whitespaceNode);
                }

                $remainingText = mb_substr($text, 0, -mb_strlen($whitespace));
                if (empty($remainingText)) {
                    $xref->removeChild($lastChild);
                } else {
                    $lastChild->nodeValue = $remainingText;
                }
            }
        }
    }

    protected function swapXrefEmphasis(DOMElement $xref): void
    {
        if ($xref->childNodes->length !== 1) {
            return;
        }

        $child = $xref->firstChild;
        if (!$child instanceof DOMElement || $child->localName !== 'emphasis') {
            return;
        }

        $emphasis = $child;
        $parent = $xref->parentNode;
        if (!$parent) {
            return;
        }

        // move <emphasis> children into before <emphasis>
        while (null !== $emphasis->firstChild) {
            $xref->appendChild($emphasis->firstChild);
        }

        // move <emphasis> before <xref>
        $parent->insertBefore($emphasis, $xref);

        // move <xref> as only child of <emphasis>
        $emphasis->appendChild($xref);
    }

    protected function parseParts(XRefLink $xref): void
    {
        $toNode = $xref->getToNode();
        $number = match (true) {
            $toNode instanceof Appendix            => $toNode->getNumber(),
            $toNode instanceof BackMatter          => '',
            $toNode instanceof Chapter             => $toNode->getNumber(),
            $toNode instanceof CopyrightPage       => $toNode->getTitle(),
            $toNode instanceof Definition          => $toNode->getTerm(),
            $toNode instanceof DefinitionList      => '',
            $toNode instanceof Figure              => $toNode->getNumber(),
            $toNode instanceof Foreword            => $toNode->getTitle(),
            $toNode instanceof FrontMatter         => '',
            $toNode instanceof Index               => $toNode->getTitle(),
            $toNode instanceof IndexDivision       => $toNode->getTitle(),
            $toNode instanceof IndexEntry          => $toNode->getTerm(),
            $toNode instanceof Preface             => $toNode->getTitle(),
            $toNode instanceof Promulgator         => $toNode->getAcronym(),
            $toNode instanceof Publication         => '',
            $toNode instanceof PublisherNote       => $toNode->getTitle(),
            $toNode instanceof ReferenceStandard   => $toNode->getNumber(),
            $toNode instanceof RelocatedFrom       => '',
            $toNode instanceof RelocatedTo         => '',
            $toNode instanceof SecondaryIndexEntry => $toNode->getTerm(),
            $toNode instanceof Section             => $toNode->getNumber(),
            $toNode instanceof Table               => $toNode->getNumber(),
            $toNode instanceof TertiaryIndexEntry  => $toNode->getTerm(),
            $toNode instanceof TitlePage           => $toNode->getTitle(),
            $toNode instanceof Volume              => $toNode->getTitle(),
            default                                => '',
        };

        $body = $xref->getOriginalBody();
        if (!empty($number) && mb_substr($body, -mb_strlen($number)) === $number) {
            $label = mb_trim(mb_substr($body, 0, mb_strlen($body) - mb_strlen($number)));
            $xref->setOriginalLabel($label);
            $xref->setOriginalNumber($number);
        }
    }

    protected function getToNode(string $rid, Publication $codeBook): ?AbstractCodeBookNode
    {
        if (empty($rid)) {
            return null;
        }

        $node = $this->nodeRepo->findOneBy([
            'ulid' => $rid,
        ]);

        if (!$node) {
            $node = $this->nodeRepo->findOneBy([
                'publication'  => $codeBook,
                'nodeIdBackup' => $rid,
            ]);
        }

        return $node;
    }
}
