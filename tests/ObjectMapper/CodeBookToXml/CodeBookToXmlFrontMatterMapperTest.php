<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFrontMatterMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlFrontMatterMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlFrontMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlFrontMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\FrontMatter $from, Xml2\FrontMatter $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\FrontMatter();
        $from->setNodeId('');
        $expected = new Xml2\FrontMatter();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);

        $codeTitlePage = new CodeBook\TitlePage();
        $codeTitlePage->setNodeId('');
        $from->setTitlePage($codeTitlePage);
        $expected->setTitlePage(new Xml2\TitlePage());
        yield 'titlePage' => [deep_copy($from), deep_copy($expected)];

        $codeCopyrightPage = new CodeBook\CopyrightPage();
        $codeCopyrightPage->setNodeId('');
        $from->setCopyrightPage($codeCopyrightPage);
        $expected->setCopyrightPage(new Xml2\CopyrightPage());
        yield 'copyrightPage' => [deep_copy($from), deep_copy($expected)];

        $codePublisherNote = new CodeBook\PublisherNote();
        $codePublisherNote->setNodeId('');
        $from->setPublisherNote($codePublisherNote);
        $expected->setPublisherNote(new Xml2\PublisherNote());
        yield 'publisherNote' => [deep_copy($from), deep_copy($expected)];

        $codePreface = new CodeBook\Preface();
        $codePreface->setNodeId('');
        $from->setPreface($codePreface);
        $expected->setPreface(new Xml2\Preface());
        yield 'preface' => [deep_copy($from), deep_copy($expected)];

        $codeForeword = new CodeBook\Foreword();
        $codeForeword->setNodeId('');
        $from->setForeword($codeForeword);
        $expected->setForeword(new Xml2\Foreword());
        yield 'foreword' => [deep_copy($from), deep_copy($expected)];

        $codeSection = new CodeBook\Section();
        $codeSection->setNodeId('');
        $from->addChild(clone $codeSection);
        $from->addChild(clone $codeSection);
        $expected->setChildren([new Xml2\Section(), new Xml2\Section()]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
