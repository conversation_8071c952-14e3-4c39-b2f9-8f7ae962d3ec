<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlReferenceStandardMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlReferenceStandardMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlReferenceStandardMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlReferenceStandardMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\ReferenceStandard $from, Xml2\Reference $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\ReferenceStandard();
        $from->setNodeId('');
        $expected = new Xml2\Reference();
        $titleGroup = new Xml2\TitleGroup();
        $expected->setTitleGroup($titleGroup);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);

        $from->setNumber('number');
        $number = new Xml2\Number();
        $number->setBody('number');
        $titleGroup->setNumber($number);
        yield 'number' => [deep_copy($from), deep_copy($expected)];

        $from->setTitle('title');
        $title = new Xml2\Title();
        $title->setBody('title');
        $titleGroup->setTitle($title);
        yield 'title' => [deep_copy($from), deep_copy($expected)];

        $from->setNavPointerGroup('navPointerGroup');
        $expected->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($from), deep_copy($expected)];
    }
}
