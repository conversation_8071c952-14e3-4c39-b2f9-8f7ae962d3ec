<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlIndexMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlIndexMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Index $from, Xml2\Index $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Index();
        $from->setNodeId('');
        $expected = new Xml2\Index();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $childSection = new CodeBook\Section();
        $childSection->setNodeId('childNode');
        $from->addChild($childSection);

        $expectedChild = new Xml2\Section();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
