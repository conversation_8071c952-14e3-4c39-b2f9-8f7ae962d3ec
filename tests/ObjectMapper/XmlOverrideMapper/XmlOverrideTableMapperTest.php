<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideTableMapper;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideTableMapper */
class XmlOverrideTableMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideTableMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideTableMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Table $element, CodeBook\Table $expected): void
    {
        $entity = new CodeBook\Table();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Table();
        $expected = new CodeBook\Table();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // TableNotes body
        $tableNotes = new TableNotes();
        $tableNotes->setBody('Table notes text');
        $element->setTableNotes($tableNotes);
        $expected->setTableNotes('Table notes text');
        yield 'tableNotes-body' => [deep_copy($element), deep_copy($expected)];

        // TableNotes + TitleGroup
        $title = new Title();
        $title->setBody('Table Notes Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $tableNotes->setTitleGroup($titleGroup);
        $expected->setTableNotesTitle('Table Notes Title');
        yield 'tableNotes-titleGroup' => [deep_copy($element), deep_copy($expected)];

        //Title group
        yield from $this->titleGroupCases($element, $expected);
    }
}
