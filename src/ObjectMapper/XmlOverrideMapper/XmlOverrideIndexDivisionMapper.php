<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverrideIndexDivisionMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\IndexDivision $element, CodeBook\IndexDivision $entity): CodeBook\IndexDivision
    {
        if ($element->getTitleGroup() && $title = $element->getTitleGroup()->getTitle()) {
            $entity->setTitle($title->getBody());
        }

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            if (!empty($children)) {
                $entity->setChildren($children);
            }
        }

        return $entity;
    }
}
