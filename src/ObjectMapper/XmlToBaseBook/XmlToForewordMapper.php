<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlToForewordMapper extends AbstractXmlToBaseBookMapper
{
    public function map(Element\Foreword $element): BaseBook\Foreword
    {
        $entity = new BaseBook\Foreword();
        $this->mapCommonAttributes($element, $entity);
        $this->mapTitleGroup($element, $entity);

        $entity->setTocEntry($element->hasTocEntry());
        $entity->setBody($element->getBody());

        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            $entity->setChildren($children);
        }

        return $entity;
    }
}
