<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Promulgator extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;

    public ?Acronym $acronym = null;
    public ?Address $address = null;
    public ?References $references = null;

    public function getAcronym(): ?Acronym
    {
        return $this->acronym;
    }

    public function setAcronym(?Acronym $acronym): void
    {
        $this->acronym = $acronym;
    }

    public function getAddress(): ?Address
    {
        return $this->address;
    }

    public function setAddress(?Address $address): void
    {
        $this->address = $address;
    }

    public function getReferences(): ?References
    {
        return $this->references;
    }

    public function setReferences(?References $references): void
    {
        $this->references = $references;
    }
}
