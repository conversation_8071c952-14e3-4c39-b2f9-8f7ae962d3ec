<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook\AbstractBaseBookNode;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CommonBook\Attribute\RevisionAttributes;
use App\Entity\CommonBook\Field\QrCode;
use App\Entity\CommonBook\Field\TitleGroup;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

abstract class AbstractMapperTestCase extends KernelTestCase
{
    protected function commonMapCases(AbstractBaseBookNode $fromNode, AbstractCodeBookNode $expected): iterable
    {
        yield from $this->commonAttributeMapCases($fromNode, $expected);
    }

    protected function commonAttributeMapCases(AbstractBaseBookNode $fromNode, AbstractCodeBookNode $expected): iterable
    {
        $fromNode->setPosition(100);
        $expected->setPosition(100);
        yield 'position' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRole('role');
        $expected->setRole('role');
        yield 'common attrs / @role' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDisplay('display');
        $expected->setDisplay('display');
        yield 'common attrs / @display' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setVerbatim(true);
        $expected->setVerbatim(true);
        yield 'common attrs / @verbatim' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setLanguage('language');
        $expected->setLanguage('language');
        yield 'common attrs / @language' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setAdditionalInfo('add info');
        $expected->setAdditionalInfo('add info');
        yield 'common attrs / @additionalInfo' => [deep_copy($fromNode), deep_copy($expected)];
    }

    protected function revisionAttributeMapCases(AbstractBaseBookNode $fromNode, AbstractCodeBookNode $expected): iterable
    {
        /** @var RevisionAttributes $fromNode  */
        /** @var RevisionAttributes $expected  */

        $fromNode->setRevisionBy('revisionBy');
        $expected->setRevisionBy('revisionBy');
        yield 'revision attrs / @revisionBy' => [deep_copy($fromNode), deep_copy($expected)];

        $dt = new DateTime();
        $fromNode->setRevisionDateTime($dt);
        $expected->setRevisionDateTime($dt);
        yield 'revision attrs / @revisionDateTime' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRevision('revision');
        $expected->setRevision('revision');
        yield 'revision attrs / @revision' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRevisionGroup('revisionGroup');
        $expected->setRevisionGroup('revisionGroup');
        yield 'revision attrs / @revisionGroup' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDataChanged('dataChanged');
        $expected->setDataChanged('dataChanged');
        yield 'revision attrs / @dataChanged' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDataChangedIn('dataChangedIn');
        $expected->setDataChangedIn('dataChangedIn');
        yield 'revision attrs / @dataChangedIn' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRelocatedFromAttr('relocatedFrom');
        $expected->setRelocatedFromAttr('relocatedFrom');
        yield 'revision attrs / @relocatedFromAttr' => [deep_copy($fromNode), deep_copy($expected)];
    }

    protected function titleGroupMapCases(AbstractBaseBookNode $fromNode, AbstractCodeBookNode $expected): iterable
    {
        /** @var TitleGroup $fromNode */
        /** @var TitleGroup $expected */

        $fromNode->setSuperTitle('superTitle');
        $expected->setSuperTitle('superTitle');
        yield 'titleGroup / superTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCommitteeDesignation('committeeDesignation');
        $expected->setCommitteeDesignation('committeeDesignation');
        yield 'titleGroup / committeeDesignation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setLabel('label');
        $expected->setLabel('label');
        yield 'titleGroup / label' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setNumber('number');
        $expected->setNumber('number');
        yield 'titleGroup / number' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCorrelated('correlated');
        $expected->setCorrelated('correlated');
        yield 'titleGroup / correlated' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitle('title');
        $expected->setTitle('title');
        yield 'titleGroup / title' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitleAbbreviation('abbr');
        $expected->setTitleAbbreviation('abbr');
        yield 'titleGroup / titleAbbreviation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitleYear('year');
        $expected->setTitleYear('year');
        yield 'titleGroup / titleYear' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setSubTitle('subTitle');
        $expected->setSubTitle('subTitle');
        yield 'titleGroup / subTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitleHistory('history');
        $expected->setTitleHistory('history');
        yield 'titleGroup / titleHistory' => [deep_copy($fromNode), deep_copy($expected)];
    }

    protected function qrCodeMapCases(AbstractBaseBookNode $fromNode, AbstractCodeBookNode $expected): iterable
    {
        /** @var QrCode $fromNode */
        /** @var QrCode $expected */

        $fromNode->setQrActive(true);
        $expected->setQrActive(true);
        yield 'qr / qrActive' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrDisplay(true);
        $expected->setQrDisplay(true);
        yield 'qr / qrDisplay' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrLevelReference('qrLevelReference');
        $expected->setQrLevelReference('qrLevelReference');
        yield 'qr / qrLevelReference' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrPurpose('qrPurpose');
        $expected->setQrPurpose('qrPurpose');
        yield 'qr / qrPurpose' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrImage('qrImage');
        $expected->setQrImage('qrImage');
        yield 'qr / qrImage' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrBookIcon('qrBookIcon');
        $expected->setQrBookIcon('qrBookIcon');
        yield 'qqr / rBookIcon' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrUrl('qrUrl');
        $expected->setQrUrl('qrUrl');
        yield 'qr / qrUrl' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrIcon('qrIcon');
        $expected->setQrIcon('qrIcon');
        yield 'qr / qrIcon' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrDepartment('qrDepartment');
        $expected->setQrDepartment('qrDepartment');
        yield 'qr / qrDepartment' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setQrBusinessUnit('qrBusinessUnit');
        $expected->setQrBusinessUnit('qrBusinessUnit');
        yield 'qr / qrBusinessUnit' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
