<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideForewordMapper;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideForewordMapper */
class XmlOverrideForewordMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverrideForewordMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideForewordMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Foreword $element, CodeBook\Foreword $expected): void
    {
        $entity = new CodeBook\Foreword();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Foreword();
        $expected = new CodeBook\Foreword();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        //Title group
        yield from $this->titleGroupCases($element, $expected);

        // TocEntry
        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($element), deep_copy($expected)];

        // Body
        $element->setBody('<p>Foreword content</p>');
        $expected->setBody('<p>Foreword content</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        // TocEntry + Body together
        $element->setTocEntry(true);
        $element->setBody('<p>Full Foreword</p>');
        $expected->setTocEntry(true);
        $expected->setBody('<p>Full Foreword</p>');
        yield 'tocEntry+body' => [deep_copy($element), deep_copy($expected)];
    }
}
