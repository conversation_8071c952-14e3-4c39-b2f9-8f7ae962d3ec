<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;

use function array_key_exists;
use function assert;
use function is_string;

class CommitteeDesignationMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'committee-desig';
    }

    public static function dtoClass(): string
    {
        return CommitteeDesignation::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof CommitteeDesignation);

        if (array_key_exists('responsible-for', $attrs) && is_string($attrs['responsible-for'])) {
            $element->setResponsibleFor($attrs['responsible-for']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof CommitteeDesignation);

        if (!empty($element->getResponsibleFor())) {
            $attrs['responsible-for'] = $element->getResponsibleFor();
        }
    }
}
