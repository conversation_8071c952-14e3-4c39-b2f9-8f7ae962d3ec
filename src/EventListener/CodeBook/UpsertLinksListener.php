<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\EventListener\CodeBook;

use App\Event\CodeBook\CodeBookNodePostPersistEvent;
use App\Event\CodeBook\CodeBookNodePostUpdateEvent;
use App\Message\CodeBook\UpsertPublicationRefLinksAsyncMessage;
use App\Message\CodeBook\UpsertUrlLinksAsyncMessage;
use App\Message\CodeBook\UpsertXRefLinksAsyncMessage;
use App\Service\StopwatchService;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Stopwatch\StopwatchEvent;

#[AsEventListener(event: CodeBookNodePostPersistEvent::class, method: 'queue')]
#[AsEventListener(event: CodeBookNodePostUpdateEvent::class, method: 'queue')]
class UpsertLinksListener
{
    private StopwatchEvent $stopwatch;

    public function __construct(
        private readonly MessageBusInterface $messageBus,
        StopwatchService                     $stopwatch,
    ) {
        $this->stopwatch = $stopwatch->create('UpsertLinksListener');
    }

    public function queue(CodeBookNodePostPersistEvent|CodeBookNodePostUpdateEvent $event): void
    {
        $this->stopwatch->start();

        // Must be run POST persist/update as these are BACKGROUND processes.
        $this->messageBus->dispatch(new UpsertUrlLinksAsyncMessage($event->getNode()));
        $this->messageBus->dispatch(new UpsertXRefLinksAsyncMessage($event->getNode()));
        // @TODO: disabled for now.
        //$this->messageBus->dispatch(new UpsertPublicationRefLinksAsyncMessage($event->getNode()));

        $this->stopwatch->stop();
    }
}
