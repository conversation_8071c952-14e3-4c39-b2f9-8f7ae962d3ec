<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverridePublisherNoteMapper;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverridePublisherNoteMapper */
class XmlOverridePublisherNoteMapperTest extends KernelTestCase
{
    use OverrideMapperTestCases;

    private ?XmlOverridePublisherNoteMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverridePublisherNoteMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(PublisherNote $element, CodeBook\PublisherNote $expected): void
    {
        $entity = new CodeBook\PublisherNote();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new PublisherNote();
        $expected = new CodeBook\PublisherNote();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        //Title group
        yield from $this->titleGroupCases($element, $expected);

        // TocEntry = true
        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($element), deep_copy($expected)];

        // Body
        $element->setBody('<p>Publisher note body</p>');
        $expected->setBody('<p>Publisher note body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        // TocEntry + Body together
        $element->setTocEntry(true);
        $element->setBody('<p>Full Publisher Note</p>');
        $expected->setTocEntry(true);
        $expected->setBody('<p>Full Publisher Note</p>');
        yield 'tocEntry+body' => [deep_copy($element), deep_copy($expected)];
    }
}
