<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlIndexEntryMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\IndexEntry $from, Xml2\IndexEntry $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\IndexEntry();
        $from->setNodeId('');
        $expected = new Xml2\IndexEntry();
        $primaryIe = new Xml2\PrimaryIndexEntry();
        $expected->setPrimaryIndexEntry($primaryIe);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);

        $from->setReferenceId('referenceId');
        $primaryIe->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($from), deep_copy($expected)];

        $from->setTerm('term');
        $term = new Xml2\Term();
        $term->setBody('term');
        $primaryIe->setTerm($term);
        yield 'term' => [deep_copy($from), deep_copy($expected)];

        $from->setNavPointerGroup('navPointerGroup');
        $primaryIe->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($from), deep_copy($expected)];

        $childSection = new CodeBook\SecondaryIndexEntry();
        $childSection->setNodeId('childNode');
        $from->addChild($childSection);

        $expectedChild = new Xml2\SecondaryIndexEntry();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
