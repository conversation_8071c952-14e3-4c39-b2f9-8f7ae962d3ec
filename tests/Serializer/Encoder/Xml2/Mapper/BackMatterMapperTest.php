<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Mapper\BackMatterMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\BackMatterMapper
 */
class BackMatterMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(BackMatter::class, $service->classMap);
        $this->assertIsCallable($service->classMap[BackMatter::class]);
        $this->assertInstanceOf(BackMatterMapper::class, $service->classMap[BackMatter::class][0]);

        $this->assertArrayHasKey(BackMatterMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[BackMatterMapper::elementName()]);
        $this->assertInstanceOf(BackMatterMapper::class, $service->elementMap[BackMatterMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(BackMatter $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<backmatter %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new BackMatter();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(BackMatter $element, string $innerXml): void
    {
        $xml = sprintf('<backmatter xmlns="%s" xmlns:m="%s">%s</backmatter>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new BackMatter();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setChildren([new Appendix()]);
        $innerXml .= '<appendix/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $element->setChildren([new Appendix(), new Appendix()]);
        $innerXml .= '<appendix/>';
        yield '2 children' => [deep_copy($element), $innerXml];

        $element->setChildren([new Appendix(), new Appendix(), new Level()]);
        $innerXml .= '<level-1/>';
        yield 'level child' => [deep_copy($element), $innerXml];

        $element->setChildren([new Appendix(), new Appendix(), new Level(), new Index()]);
        $innerXml .= '<index/>';
        yield 'index child' => [deep_copy($element), $innerXml];
    }
}
