<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

class XmlOverrideReferenceStandardMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\Reference $element, CodeBook\ReferenceStandard $entity): CodeBook\ReferenceStandard
    {
        if ($titleGroup = $element->getTitleGroup()) {
            if ($number = $titleGroup->getNumber()) {
                $entity->setNumber($number->getBody());
            }
            if ($title = $titleGroup->getTitle()) {
                $entity->setTitle($title->getBody());
            }
        }

        $entity->setNavPointerGroup($element->getNavPointerGroup());

        return $entity;
    }
}
