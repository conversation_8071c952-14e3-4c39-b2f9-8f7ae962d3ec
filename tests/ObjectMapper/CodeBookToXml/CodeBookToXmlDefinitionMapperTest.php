<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlDefinitionMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlDefinitionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlDefinitionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Definition $from, Xml2\DefinitionItem $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\Definition();
        $from->setNodeId('');
        $expected = new Xml2\DefinitionItem();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield 'indexNumber' => [deep_copy($from), deep_copy($expected)];

        $from->setCommitteeDesignation('committeeDesignation');
        $cd = new Xml2\CommitteeDesignation();
        $cd->setBody('committeeDesignation');
        $expected->setCommitteeDesignation($cd);
        yield 'committeeDesignation' => [deep_copy($from), deep_copy($expected)];

        $from->setTerm('term');
        $term = new Xml2\Term();
        $term->setBody('term');
        $expected->setTerm($term);
        yield 'term' => [deep_copy($from), deep_copy($expected)];

        $from->setDefinition('definition');
        $definition = new Xml2\Definition();
        $definition->setBody('definition');
        $expected->setDefinition($definition);
        yield 'definition' => [deep_copy($from), deep_copy($expected)];
    }
}
