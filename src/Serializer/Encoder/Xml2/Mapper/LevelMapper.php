<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\Disclaimer;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function array_merge;
use function assert;
use function settype;
use function str_starts_with;

class LevelMapper extends AbstractMapper
{
    public int $level = 1;

    public function __construct(int $level = 1)
    {
        $this->level = $level;
    }

    public static function elementName(int $level = 1): string
    {
        return AbstractMapper::CLARK . static::localName($level);
    }

    public static function localName(int $level = 1): string
    {
        return 'level-' . $level;
    }

    public static function dtoClass(): string
    {
        return Level::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Level);

        $element->displayLevel = $this->level;

        $this->readStringAttributes($element, $attrs, ['indexNumber' => 'indexnum']);
        $this->readBooleanAttributes($element, $attrs, [
            'tocEntry'   => 'tocentry',
            'tocAutoAdd' => 'tocautoadd',
        ]);

        if (array_key_exists('reservecount', $attrs) && settype($attrs['reservecount'], 'integer')) {
            $element->reserveCount = (int) $attrs['reservecount'];
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof Level);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('metadata' === $key && $value instanceof Metadata) {
                $element->setMetadata($value);
            } elseif ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('QR-code' === $key && $value instanceof QrCode) {
                $element->setQrCode($value);
            } elseif ('history' === $key && $value instanceof History) {
                $element->setHistory($value);
            } elseif ('objectives' === $key && $value instanceof Objectives) {
                $element->setObjectives($value);
            } elseif ('section-body' === $key && $value instanceof SectionBody) {
                $element->setBody($value);
                $children = array_merge($children, $value->getChildren());
            } elseif (str_starts_with($key, 'level-') && $value instanceof Level) {
                $children[] = $value;
            } elseif ('section' === $key && $value instanceof Section) {
                $children[] = $value;
            } elseif ('relocated-to' === $key && $value instanceof RelocatedTo) {
                $children[] = $value;
            } elseif ('relocated-from' === $key && $value instanceof RelocatedFrom) {
                $children[] = $value;
            } elseif ('backmatter' === $key && $value instanceof BackMatter) {
                $element->setBackMatter($value);
            } elseif ('disclaimer' === $key && $value instanceof Disclaimer) {
                // TBD. Ignore it currently.
            } else {
                throw new \RuntimeException(sprintf('Unexpected key "%s".', $key));
            }
        }
        $element->setChildren($children);
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Level);

        $this->writeStringAttributes($element, $attrs, ['indexNumber' => 'indexnum']);
        $this->writeBooleanAttributes($element, $attrs, [
            'tocEntry'   => 'tocentry',
            'tocAutoAdd' => 'tocautoadd',
        ]);

        if ($element->reserveCount > 0) {
            $attrs['reservecount'] = (string) $element->reserveCount;
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof Level);

        $bodyChildren = [];
        $children = [];

        foreach ($element->getChildren() as $i) {
            if ($i instanceof DefinitionList || $i instanceof Figure || $i instanceof Promulgator || $i instanceof Table) {
                $bodyChildren[] = $i;
            } else {
                $children[] = $i;
            }
        }

        if ($element->getMetadata()) {
            $writer->writeElement(self::CLARK . 'metadata', $element->getMetadata());
        }
        if ($element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $qrCode = $element->getQrCode();
        if ($qrCode && $qrCode->getImg()) {
            $qrCode = $element->getQrCode();

            $writer->startElement(self::CLARK . 'QR-code');
            $writer->writeAttributes([
                'id'       => $element->getId(),
                'display'  => $qrCode->isQrDisplay() ? 'true' : 'false',
                'levelref' => $qrCode->getLevelReference(),
                'purpose'  => $qrCode->getPurpose(),
            ]);

            if ($qrCode->getImg()) {
                $imgSrc = $qrCode->getImg()->getSrc();
                $writer->startElement(self::CLARK . 'img');
                $writer->writeAttributes([
                    'src' => $imgSrc,
                    'alt' => 'QR-Code',
                ]);
                $writer->endElement();
            }

            if ($qrCode->getShortCode()) {
                $shortUrl = $qrCode->getShortCode()->getBody();
                $writer->startElement(self::CLARK . 'short-code');
                $writer->writeAttributes([
                    'short-url' => $shortUrl,
                ]);
                $writer->endElement();
            }

            if ($qrCode->getBookIcon()) {
                $iconSrc = $qrCode->getBookIcon()->getSrc();
                $writer->startElement(self::CLARK . 'book-icon');
                $writer->writeAttributes([
                    'src' => $iconSrc,
                ]);
                $writer->endElement();
            }
            $writer->endElement();
        }
        if ($element->getHistory()) {
            $writer->writeElement(self::CLARK . 'history', $element->getHistory());
        }
        if ($element->getObjectives()) {
            $writer->writeElement(self::CLARK . 'objectives', $element->getObjectives());
        }
        if ($element->getBody() || !empty($bodyChildren)) {
            $body = $element->getBody() ?? new SectionBody();
            $body->setchildren($bodyChildren);
            $writer->writeElement(self::CLARK . 'section-body', $body);
        }

        foreach ($children as $i) {
            $newLevel = $element->getDisplayLevel() + 1;
            if ($i instanceof Level) {
                $i->setDisplayLevel($newLevel);
                $writer->writeElement(self::CLARK . 'level-' . $newLevel, $i);
            } elseif ($i instanceof Section) {
                $i->setDisplayLevel($newLevel);
                $writer->writeElement(self::CLARK . 'section', $i);
            } elseif ($i instanceof Figure) {
                $writer->writeElement(self::CLARK . 'figure', $i);
            } elseif ($i instanceof Table) {
                $writer->writeElement(self::CLARK . 'formal-table', $i);
            } elseif ($i instanceof RelocatedTo) {
                $writer->writeElement(self::CLARK . 'relocated-to', $i);
            } elseif ($i instanceof RelocatedFrom) {
                $writer->writeElement(self::CLARK . 'relocated-from', $i);
            }
        }

        if ($element->getBackMatter()) {
            $writer->writeElement(self::CLARK . 'backmatter', $element->getBackMatter());
        }
    }
}
