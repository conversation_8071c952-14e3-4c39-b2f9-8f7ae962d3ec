<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element;

use function array_map;

class XmlOverrideDefinitionListMapper extends AbstractXmlOverrideMapper
{
    public function map(Element\DefinitionList $element, CodeBook\DefinitionList $entity): CodeBook\DefinitionList
    {
        if ($this->mapper) {
            $children = array_map(fn($i) => $this->mapper->map($i), $element->getChildren());
            $entity->setChildren($children);
        }

        return $entity;
    }
}
