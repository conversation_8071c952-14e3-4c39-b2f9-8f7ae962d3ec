<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSecondaryIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlSecondaryIndexEntryMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlSecondaryIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlSecondaryIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\SecondaryIndexEntry $from, Xml2\SecondaryIndexEntry $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\SecondaryIndexEntry();
        $from->setNodeId('');
        $expected = new Xml2\SecondaryIndexEntry();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);

        $from->setReferenceId('referenceId');
        $expected->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($from), deep_copy($expected)];

        $from->setTerm('term');
        $term = new Xml2\Term();
        $term->setBody('term');
        $expected->setTerm($term);
        yield 'term' => [deep_copy($from), deep_copy($expected)];

        $from->setNavPointerGroup('navPointerGroup');
        $expected->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($from), deep_copy($expected)];
    }
}
