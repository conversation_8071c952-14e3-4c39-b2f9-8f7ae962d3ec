<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Appendix extends AbstractElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;
    use Attribute\IndexNumber;
    use Attribute\TocEntry;
    use Attribute\TocAutoAdd;
    use HasChildren;

    use Field\TitleGroupTrait;
    use Field\QrCodeTrait;

    public ?Metadata $metadata = null;
    public ?History $history = null;
    public ?Objectives $objectives = null;
    public ?Note $note = null;
    public ?AbstractField $abstract = null;
    public ?Keywords $keywords = null;
    public ?SectionBody $body = null;

    public function getMetadata(): ?Metadata
    {
        return $this->metadata;
    }

    public function setMetadata(?Metadata $metadata): void
    {
        $this->metadata = $metadata;
    }

    public function getObjectives(): ?Objectives
    {
        return $this->objectives;
    }

    public function setObjectives(?Objectives $objectives): void
    {
        $this->objectives = $objectives;
    }

    public function getHistory(): ?History
    {
        return $this->history;
    }

    public function setHistory(?History $history): void
    {
        $this->history = $history;
    }

    public function getNote(): ?Note
    {
        return $this->note;
    }

    public function setNote(?Note $note): void
    {
        $this->note = $note;
    }

    public function getAbstract(): ?AbstractField
    {
        return $this->abstract;
    }

    public function setAbstract(?AbstractField $abstract): void
    {
        $this->abstract = $abstract;
    }

    public function getKeywords(): ?Keywords
    {
        return $this->keywords;
    }

    public function setKeywords(?Keywords $keywords): void
    {
        $this->keywords = $keywords;
    }

    public function getBody(): ?SectionBody
    {
        return $this->body;
    }

    public function setBody(?SectionBody $body): void
    {
        $this->body = $body;
    }
}
