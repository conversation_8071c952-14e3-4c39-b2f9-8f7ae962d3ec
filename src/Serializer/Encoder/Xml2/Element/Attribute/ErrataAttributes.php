<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element\Attribute;

trait ErrataAttributes
{
    public string $errata = '';
    public string $errataList = '';

    public function getErrata(): string
    {
        return $this->errata;
    }

    public function setErrata(string $errata): void
    {
        $this->errata = $errata;
    }

    public function getErrataList(): string
    {
        return $this->errataList;
    }

    public function setErrataList(string $errataList): void
    {
        $this->errataList = $errataList;
    }
}
