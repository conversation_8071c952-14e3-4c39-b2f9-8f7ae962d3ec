<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookVolumeMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookVolumeMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookVolumeMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookVolumeMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Volume $fromNode, CodeBook\Volume $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
            $i->setNodeId('nodeId');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Volume();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Volume();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setTitleType(TitleType::CODE);
        $expected->setTitleType(TitleType::CODE);
        yield 'titleType' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setGoverningType(GoverningType::CITY);
        $expected->setGoverningType(GoverningType::CITY);
        yield 'governingType' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setMetaTitle('metaTitle');
        $expected->setMetaTitle('metaTitle');
        yield 'metaTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setEdition('metaTitle');
        $expected->setEdition('metaTitle');
        yield 'edition' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setParentDocument('parentDocument');
        $expected->setParentDocument('parentDocument');
        yield 'parentDocument' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setPublicationId('publicationId');
        $expected->setPublicationId('publicationId');
        yield 'publicationId' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setYear('year');
        $expected->setYear('year');
        yield 'year' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setPublicationAbbreviation('publicationAbbreviation');
        $expected->setPublicationAbbreviation('publicationAbbreviation');
        yield 'publicationAbbreviation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setVersion('version');
        $expected->setVersion('version');
        yield 'version' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDateOrigin('dateOrigin');
        $expected->setDateOrigin('dateOrigin');
        yield 'dateOrigin' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setOrigin('origin');
        $expected->setOrigin('origin');
        yield 'origin' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setDateUpdated('dateUpdated');
        $expected->setDateUpdated('dateUpdated');
        yield 'dateUpdated' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setModifiedBy('modifiedBy');
        $expected->setModifiedBy('modifiedBy');
        yield 'modifiedBy' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setChildren([$fmFrom = new BaseBook\FrontMatter(), $cFrom = new BaseBook\Chapter(), $bmFrom = new BaseBook\BackMatter()]);
        $fromNode->setFrontMatter($fmFrom);
        $fromNode->setBackMatter($bmFrom);
        $cFrom->setRole('chapter');
        $expected->setChildren([$fmTo = new CodeBook\FrontMatter(), $cTo = new CodeBook\Chapter(), $bmTo = new CodeBook\BackMatter()]);
        $expected->setFrontMatter($fmTo);
        $expected->setBackMatter($bmTo);
        $cTo->setRole('chapter');
        yield 'children' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
