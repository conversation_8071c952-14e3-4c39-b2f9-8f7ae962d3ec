<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookDefinitionMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\Definition $from): CodeBook\Definition
    {
        $to = new CodeBook\Definition();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setIndexNumber($from->getIndexNumber());

        $to->setCommitteeDesignation($from->getCommitteeDesignation());
        $to->setTerm($from->getTerm());
        $to->setDefinition($from->getDefinition());
        $to->setCaption($from->getCaption());
        $to->setLegend($from->getLegend());
        $to->setSource($from->getSource());
        $to->setCreditTitle($from->getCreditTitle());
        $to->setCredit($from->getCredit());

        return $to;
    }
}
