<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToSecondaryIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToSecondaryIndexEntryMapper */
class XmlToSecondaryIndexEntryMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToSecondaryIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToSecondaryIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(SecondaryIndexEntry $element, BaseBook\SecondaryIndexEntry $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new SecondaryIndexEntry();
        $expected = new BaseBook\SecondaryIndexEntry();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);

        $term = new Term();
        $term->setBody('Term');
        $element->setTerm($term);
        $expected->setTerm('Term');
        yield 'term' => [deep_copy($element), deep_copy($expected)];

        $navPointerGroup = <<<XML
<nav-pointer-group>
    <nav-pointer/>
</nav-pointer-group>
XML;
        $element->setNavPointerGroup($navPointerGroup);
        $expected->setNavPointerGroup($navPointerGroup);
        yield 'nav-pointer-group' => [deep_copy($element), deep_copy($expected)];
    }
}
