<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

class NotesDto
{
    public string $codesNotes = '';
    public string $pubsNotes = '';
    public string $typesetterNotes = '';
    #[OA\Property(type: "array", items: new OA\Items(ref: new Model(type: NoteAttachmentDto::class)))]
    public array $codesAttachments = [];
    #[OA\Property(type: "array", items: new OA\Items(ref: new Model(type: NoteAttachmentDto::class)))]
    public array $pubsAttachments = [];
    #[OA\Property(type: "array", items: new OA\Items(ref: new Model(type: NoteAttachmentDto::class)))]
    public array $typesetterAttachments = [];
}
