<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverridePromulgatorMapper;
use App\Serializer\Encoder\Xml2\Element\Address;
use App\Serializer\Encoder\Xml2\Element\Acronym;
use App\Serializer\Encoder\Xml2\Element\Email;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\Url;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverridePromulgatorMapper */
class XmlOverridePromulgatorMapperTest extends KernelTestCase
{
    private ?XmlOverridePromulgatorMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverridePromulgatorMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Promulgator $element, CodeBook\Promulgator $expected): void
    {
        $entity = new CodeBook\Promulgator();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Promulgator();
        $expected = new CodeBook\Promulgator();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // Acronym
        $acronym = new Acronym();
        $acronym->setBody('ICC');
        $element->setAcronym($acronym);
        $expected->setAcronym('ICC');
        yield 'acronym' => [deep_copy($element), deep_copy($expected)];

        // Address (basic)
        $address = new Address();
        $address->setAddressLine('123 Main St');
        $address->setOrganizationName('International Code Council');
        $address->setStreet('123 Main St');
        $address->setCity('Brea');
        $address->setState('CA');
        $address->setPostalCode('92821');
        $address->setCountry('USA');
        $address->setPhone('555-1234');
        $address->setFax('555-4321');
        $element->setAddress($address);

        $expected->setAddressLine('123 Main St');
        $expected->setOrganizationName('International Code Council');
        $expected->setStreet('123 Main St');
        $expected->setCity('Brea');
        $expected->setState('CA');
        $expected->setPostalCode('92821');
        $expected->setCountry('USA');
        $expected->setPhone('555-1234');
        $expected->setFax('555-4321');
        yield 'address-basic' => [deep_copy($element), deep_copy($expected)];

        // Address + Email
        $email = new Email();
        $email->setBody('<EMAIL>');
        $address->setEmail($email);
        $expected->setEmail('<EMAIL>');
        yield 'address-email' => [deep_copy($element), deep_copy($expected)];

        // Address + URL
        $url = new Url();
        $url->setBody('https://www.iccsafe.org');
        $url->setHref('https://www.iccsafe.org');
        $url->setAlt('ICC Website');
        $address->setUrl($url);

        $expected->setUrl('https://www.iccsafe.org');
        $expected->setUrlHref('https://www.iccsafe.org');
        $expected->setUrlAlt('ICC Website');
        yield 'address-url' => [deep_copy($element), deep_copy($expected)];
    }
}
