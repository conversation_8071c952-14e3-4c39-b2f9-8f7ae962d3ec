<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookRelocatedToMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\RelocatedTo $from): CodeBook\RelocatedTo
    {
        $to = new CodeBook\RelocatedTo();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setIndent($from->isIndent());
        $to->setAlign($from->getAlign());
        $to->setReferenceId($from->getReferenceId());

        $to->setBody($from->getBody());
        $to->setRelocatedTo($from->getRelocatedTo());

        return $to;
    }
}
