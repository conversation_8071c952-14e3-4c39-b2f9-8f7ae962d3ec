<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideDefinitionListMapper;
use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideDefinitionListMapper */
class XmlOverrideDefinitionListMapperTest extends KernelTestCase
{
    private ?XmlOverrideDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideDefinitionListMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(DefinitionList $element, CodeBook\DefinitionList $expected): void
    {
        $entity = new CodeBook\DefinitionList();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new DefinitionList();
        $expected = new CodeBook\DefinitionList();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        yield 'base' => [deep_copy($element), deep_copy($expected)];

        $element->setId('nodeId');
        $expected->setNodeId('nodeId');

        yield '@id' => [deep_copy($element), deep_copy($expected)];
    }
}
