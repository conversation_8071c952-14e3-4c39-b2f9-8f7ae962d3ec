<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

trait Disclaimer
{
    public string $disclaimer = '';

    public function getDisclaimer(): string
    {
        return $this->disclaimer;
    }

    public function setDisclaimer(string $disclaimer): void
    {
        $this->disclaimer = $disclaimer;
    }
}
