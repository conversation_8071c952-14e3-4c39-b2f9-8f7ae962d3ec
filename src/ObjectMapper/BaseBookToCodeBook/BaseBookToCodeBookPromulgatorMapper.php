<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookPromulgatorMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\Promulgator $from): CodeBook\Promulgator
    {
        $to = new CodeBook\Promulgator();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setAcronym($from->getAcronym());
        $to->setAddressLine($from->getAddressLine());
        $to->setOrganizationName($from->getOrganizationName());
        $to->setStreet($from->getStreet());
        $to->setCity($from->getCity());
        $to->setState($from->getState());
        $to->setPostalCode($from->getPostalCode());
        $to->setCountry($from->getCountry());
        $to->setUrl($from->getUrl());
        $to->setUrlHref($from->getUrlHref());
        $to->setUrlAlt($from->getUrlAlt());
        $to->setEmail($from->getEmail());
        $to->setPhone($from->getPhone());
        $to->setFax($from->getFax());
        $to->setInternational($from->getInternational());

        if ($this->mapper) {
            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }
        }

        return $to;
    }
}
