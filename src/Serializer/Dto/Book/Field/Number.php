<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book\Field;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @since 1.11
 */
trait Number
{
    #[Groups(['list:contents'])]
    public string $number = '';

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): void
    {
        $this->number = $number;
    }
}
