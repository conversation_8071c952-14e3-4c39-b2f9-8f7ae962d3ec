<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\BaseBook;

use App\Entity\CommonBook\DefinitionListFields;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(readOnly: true)]
class DefinitionList extends AbstractBaseBookNode
{
    use DefinitionListFields;
}
