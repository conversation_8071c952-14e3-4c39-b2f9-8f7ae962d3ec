<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Enum\QrCodePurpose;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\ShortCode;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function array_key_exists;
use function assert;
use function in_array;
use function is_string;

/**
 * @since 1.11
 */
class QrCodeMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'QR-code';
    }

    public static function dtoClass(): string
    {
        return QrCode::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof QrCode);

        if (array_key_exists('display', $attrs) && is_string($attrs['display'])) {
            $element->setQrDisplay('yes' === $attrs['display']);
        }
        if (array_key_exists('levelref', $attrs) && is_string($attrs['levelref'])) {
            $element->setLevelReference($attrs['levelref']);
        }
        if (array_key_exists('purpose', $attrs) && in_array($attrs['purpose'], QrCodePurpose::cases())) {
            $element->setPurpose($attrs['purpose']);
        }
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof QrCode);

        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('img' === $key && is_string($value)) {
                $element->setImg($value);
            } elseif ('short-code' === $key && $value instanceof ShortCode) {
                $element->setShortCode($value);
            } elseif ('book-icon' === $key && is_string($value)) {
                $element->setBookIcon($value);
            }
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof QrCode);

        $attrs['display'] = $element->isQrDisplay() ? 'yes' : 'no';
        $attrs['levelref'] = $element->getLevelReference();
        $attrs['purpose'] = $element->getPurpose();
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof QrCode);

        if (!empty($element->getImg())) {
            $writer->writeRaw($element->getImg());
        }
        if ($element->getShortCode()) {
            $writer->writeElement(self::CLARK . 'short-code', $element->getShortCode());
        }
        if (!empty($element->getBookIcon())) {
            $writer->writeRaw($element->getBookIcon());
        }
    }
}
