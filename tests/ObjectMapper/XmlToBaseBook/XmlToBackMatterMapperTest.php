<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToBackMatterMapper;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlToBaseBook\XmlToBackMatterMapper */
class XmlToBackMatterMapperTest extends KernelTestCase
{
    use MapperCasesTrait;

    private ?XmlToBackMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToBackMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BackMatter $element, BaseBook\BackMatter $expected): void
    {
        $actual = $this->mapper->map($element);
        $actual->setUlid('uuid');

        foreach ($actual->getChildren() as $i) {
            $i->setUlid('uuid');
        }
        foreach ($expected->getChildren() as $i) {
            $i->setUlid('uuid');
        }

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new BackMatter();
        $expected = new BaseBook\BackMatter();
        $expected->setUlid('uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);

        $element->setChildren([new Appendix()]);
        $expected->setChildren([new BaseBook\Appendix()]);
        yield '1 child' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Appendix(), new Appendix()]);
        $expected->setChildren([new BaseBook\Appendix(), new BaseBook\Appendix()]);
        yield '2 children' => [deep_copy($element), deep_copy($expected)];

        $element->setChildren([new Appendix(), new Appendix(), new Index()]);
        $expected->setChildren([new BaseBook\Appendix(), new BaseBook\Appendix(), new BaseBook\Index()]);
        yield 'index child' => [deep_copy($element), deep_copy($expected)];
    }
}
