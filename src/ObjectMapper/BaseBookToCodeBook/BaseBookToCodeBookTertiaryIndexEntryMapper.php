<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;

class BaseBookToCodeBookTertiaryIndexEntryMapper extends AbstractBaseBookToCodeBookMapper
{
    public function map(BaseBook\TertiaryIndexEntry $from): CodeBook\TertiaryIndexEntry
    {
        $to = new CodeBook\TertiaryIndexEntry();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapQrCode($from, $to);

        $to->setReferenceId($from->getReferenceId());
        $to->setTerm($from->getTerm());
        $to->setNavPointerGroup($from->getNavPointerGroup());

        return $to;
    }
}
