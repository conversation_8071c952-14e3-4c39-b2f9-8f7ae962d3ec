<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
abstract class AbstractElement
{
    public string $id = '';
    public string $ctXmlId = '';
    private int $internalId = 0;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getCtXmlId(): string
    {
        return $this->ctXmlId;
    }

    public function setCtXmlId(string $ctXmlId): void
    {
        $this->ctXmlId = $ctXmlId;
    }

    public function getInternalId(): int
    {
        return $this->internalId;
    }

    public function setInternalId(int $internalId): void
    {
        $this->internalId = $internalId;
    }
}
