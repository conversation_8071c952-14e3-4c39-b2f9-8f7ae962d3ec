<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideRelocatedToMapper;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideRelocatedToMapper */
class XmlOverrideRelocatedToMapperTest extends KernelTestCase
{
    private ?XmlOverrideRelocatedToMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideRelocatedToMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(RelocatedTo $element, CodeBook\RelocatedTo $expected): void
    {
        $entity = new CodeBook\RelocatedTo();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new RelocatedTo();
        $expected = new CodeBook\RelocatedTo();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        // Base case
        yield 'base' => [deep_copy($element), deep_copy($expected)];

        // Indent = true
        $element->setIndent(true);
        $expected->setIndent(true);
        yield 'indent-true' => [deep_copy($element), deep_copy($expected)];

        // Align
        $element->setAlign('center');
        $expected->setAlign('center');
        yield 'align' => [deep_copy($element), deep_copy($expected)];

        // ReferenceId
        $element->setReferenceId('ref-002');
        $expected->setReferenceId('ref-002');
        yield 'referenceId' => [deep_copy($element), deep_copy($expected)];

        // RelocatedTo
        $element->setRelocatedTo('Chapter 3 Section 105');
        $expected->setRelocatedTo('Chapter 3 Section 105');
        yield 'relocatedTo' => [deep_copy($element), deep_copy($expected)];

        // All fields together
        $element->setIndent(true);
        $element->setAlign('left');
        $element->setReferenceId('RT-500');
        $element->setRelocatedTo('IBC2027 Ch5 Sec501');
        $expected->setIndent(true);
        $expected->setAlign('left');
        $expected->setReferenceId('RT-500');
        $expected->setRelocatedTo('IBC2027 Ch5 Sec501');
        yield 'all-fields' => [deep_copy($element), deep_copy($expected)];
    }
}
