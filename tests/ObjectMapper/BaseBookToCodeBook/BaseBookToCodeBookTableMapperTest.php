<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\ObjectMapper\BaseBookToCodeBook\BaseBookToCodeBookTableMapper;

use function DeepCopy\deep_copy;

class BaseBookToCodeBookTableMapperTest extends AbstractMapperTestCase
{
    private ?BaseBookToCodeBookTableMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(BaseBookToCodeBookTableMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BaseBook\Table $fromNode, CodeBook\Table $expected): void
    {
        $actual = $this->mapper->map($fromNode);
        $actual->setUlid('uuid');
        $actual->setNodeId('nodeId');
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $fromNode = new BaseBook\Table();
        $fromNode->setUlid('uuid');
        $expected = new CodeBook\Table();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');
        $expected->setRole('');
        yield 'baseline' => [deep_copy($fromNode), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($fromNode, $expected);
        yield from $this->revisionAttributeMapCases($fromNode, $expected);
        yield from $this->titleGroupMapCases($fromNode, $expected);
        yield from $this->qrCodeMapCases($fromNode, $expected);

        $fromNode->setFloat(FloatEnum::TOP);
        $expected->setFloat(FloatEnum::TOP);
        yield 'float' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'orientation' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setFrame(Frame::TOP);
        $expected->setFrame(Frame::TOP);
        yield 'frame' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setColumnSeparator(true);
        $expected->setColumnSeparator(true);
        yield 'columnSeparator' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRowSeparator(true);
        $expected->setRowSeparator(true);
        yield 'rowSeparator' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setBackgroundColor('backgroundColor');
        $expected->setBackgroundColor('backgroundColor');
        yield 'backgroundColor' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTableStyle('tableStyle');
        $expected->setTableStyle('tableStyle');
        yield 'tableStyle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setPageWide(true);
        $expected->setPageWide(true);
        yield 'pageWide' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setClass('class');
        $expected->setClass('class');
        yield 'class' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTitleAttr('titleAttr');
        $expected->setTitleAttr('titleAttr');
        yield 'titleAttr' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setSummary('summary');
        $expected->setSummary('summary');
        yield 'summary' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setWidth('width');
        $expected->setWidth('width');
        yield 'width' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setBorder('border');
        $expected->setBorder('border');
        yield 'border' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCellSpacing(100);
        $expected->setCellSpacing(100);
        yield 'cellSpacing' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCellPadding(100);
        $expected->setCellPadding(100);
        yield 'cellPadding' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setRules('rules');
        $expected->setRules('rules');
        yield 'rules' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTable('table');
        $expected->setTable('table');
        yield 'table' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCaption('caption');
        $expected->setCaption('caption');
        yield 'caption' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setLegend('legend');
        $expected->setLegend('legend');
        yield 'legend' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setSource('source');
        $expected->setSource('source');
        yield 'source' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCreditTitle('creditTitle');
        $expected->setCreditTitle('creditTitle');
        yield 'creditTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setCredit('credit');
        $expected->setCredit('credit');
        yield 'credit' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTableNotesTitle('tableNotesTitle');
        $expected->setTableNotesTitle('tableNotesTitle');
        yield 'tableNotesTitle' => [deep_copy($fromNode), deep_copy($expected)];

        $fromNode->setTableNotes('tableNotes');
        $expected->setTableNotes('tableNotes');
        yield 'tableNotes' => [deep_copy($fromNode), deep_copy($expected)];
    }
}
