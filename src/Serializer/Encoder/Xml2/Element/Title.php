<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Element;

/**
 * @since 1.11
 */
class Title extends AbstractContentElement
{
    use Attribute\CommonAttributes;
    use Attribute\RevisionAttributes;

    public string $titleAbbreviation = '';
    public string $titleYear = '';

    public function getTitleAbbreviation(): string
    {
        return $this->titleAbbreviation;
    }

    public function setTitleAbbreviation(string $titleAbbreviation): void
    {
        $this->titleAbbreviation = $titleAbbreviation;
    }

    public function getTitleYear(): string
    {
        return $this->titleYear;
    }

    public function setTitleYear(string $titleYear): void
    {
        $this->titleYear = $titleYear;
    }
}
