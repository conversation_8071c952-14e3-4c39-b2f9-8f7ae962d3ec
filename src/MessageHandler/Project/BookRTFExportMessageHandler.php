<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\Project;

use App\Entity\JobStatus;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Message\Project\BookRTFExportMessage;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\JobStatusService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Twig\Environment;

use function date;
use function file_put_contents;
use function mkdir;
use function sprintf;
use function strpos;
use function unlink;

#[AsMessageHandler]
class BookRTFExportMessageHandler
{
    public function __construct(
        private readonly LoggerInterface        $logger,
        private readonly Environment            $twig,
        private readonly JobStatusService       $statusService,
        private readonly EntityManagerInterface $em,
        private readonly string                 $kernelRootDir,
        private readonly string                 $apiUrl,
        private readonly Xml2Encoder            $xml2Encoder
    ) {
    }

    public function __invoke(BookRTFExportMessage $message): void
    {
        $this->render(
            $message,
            $this->statusService->createExportStatus(
                $message->getProject()->getShortCode(),
                JobStatusService::BOOK_RTF_EXPORT_TYPE,
                $message->isClean()
            )
        );
    }

    public function render(BookRTFExportMessage $message, JobStatus $jobStatus): int
    {
        $project = $this->em->getRepository(Project::class)->findOneBy([
            'id' => $message->getProject()->getId(),
        ]);
        $nodeId = $project->getShortCode();

        $this->logger->info(sprintf('Begin exporting book content for "%s"...', $nodeId));
        $error = false;
        $errorMessage = '';
        $fileName = $this->getFileName($message);

        $xml = $this->xml2Encoder->encode($project->getCodeBook());
        $xml = preg_replace('#\sepub:[\w-]+="[^"]*"#', '', $xml);

        $html = $this->twig->render(
            'report/bookRtfExport.html.twig', [
                'html'              => $xml,
                'reportTitle'       => $project->getShortCode(),
                'format'            => 'docx',
                'schemeAndHttpHost' => $this->apiUrl,
                'projectLevelClass' => '',
            ]
        );

        throw new ApiException('RTF support disabled');

//        file_put_contents($fileName, $content);
//
//        try {
//            $xml = $this->xml2Encoder->encode($project->getCodeBook());
//            $xml = preg_replace('#\sepub:[\w-]+="[^"]*"#', '', $xml);
//
//            $html = $this->twig->render(
//                'report/bookRtfExport.html.twig', [
//                    'html'              => $xml,
//                    'reportTitle'       => $project->getShortCode(),
//                    'format'            => 'docx',
//                    'schemeAndHttpHost' => $this->apiUrl,
//                    'projectLevelClass' => '',
//                ]
//            );
//
//            file_put_contents($fileName, $content);
//
//        } catch (\Exception $e) {
//            $errorMessage = sprintf('Export for %s Error - %s', $nodeId, $e->getMessage());
//            $this->logger->critical($errorMessage);
//            $error = true;
//        } finally {
//            $this->logger->info(
//                sprintf('Finished exporting rtf export for "%s"...', $project->getShortCode())
//            );
//            $status = $error ? JobStatus::STATUS_FAILED : JobStatus::STATUS_COMPLETED;
//            $this->statusService->updateExportStatus($jobStatus, $status, $errorMessage);
//        }
//
//        return 0;
    }

    private function getFileName(BookRTFExportMessage $message): string
    {
        $project = $message->getProject();
        $folder = sprintf('export-rtf-xml2%s', $message->isClean() ? '-clean' : '');
        $path = sprintf('%s/public/files/book-%s', $this->kernelRootDir, $folder);

        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        $this->removeOldFile($path, $project->getShortCode());
        date_default_timezone_set('America/Chicago');

        return sprintf(
            '%s/%s-%s-(%s).docx',
            $path,
            $project->getShortCode(),
            date('F_j-Y_g:ia'),
            $message->getUserName()
        );
    }

    public function removeOldFile(string $path, string $nodeId): void
    {
        $iterator = new \DirectoryIterator($path);
        foreach ($iterator as $file) {
            if (strpos($file->getFilename(), $nodeId) !== false) {
                unlink($path . '/' . $file);
            }
        }
    }
}
