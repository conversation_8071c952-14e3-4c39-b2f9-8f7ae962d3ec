<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\FigureNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function is_string;
use function trim;

/**
 * @since 1.11
 */
class FigureNotesMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'figure-notes';
    }

    public static function dtoClass(): string
    {
        return FigureNotes::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof FigureNotes);

        $notes = '';
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('figure-note' === $key && is_string($value)) {
                $notes .= "\n" . $value;
            }
        }

        $element->setBody(trim($notes));
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof FigureNotes);

        if (null !== $element->getTitleGroup()) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }

        $writer->writeRaw($element->getBody());
    }
}
