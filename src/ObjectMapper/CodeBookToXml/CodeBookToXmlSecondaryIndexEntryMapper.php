<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlSecondaryIndexEntryMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\SecondaryIndexEntry $from): Xml2\SecondaryIndexEntry
    {
        $to = new Xml2\SecondaryIndexEntry();
        $this->mapCommonAttributes($from, $to);

        if (!empty($from->getReferenceId())) {
            $to->setReferenceId($from->getReferenceId());
        }

        if (!empty($from->getTerm())) {
            $term = new Xml2\Term();
            $term->setBody($from->getTerm());
            $to->setTerm($term);
        }

        if (!empty($from->getNavPointerGroup())) {
            $to->setNavPointerGroup($from->getNavPointerGroup());
        }

        return $to;
    }
}
