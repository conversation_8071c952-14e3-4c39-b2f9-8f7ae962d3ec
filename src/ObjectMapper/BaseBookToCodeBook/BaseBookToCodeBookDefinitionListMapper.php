<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

class BaseBookToCodeBookDefinitionListMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\DefinitionList $from): CodeBook\DefinitionList
    {
        $to = new CodeBook\DefinitionList();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);
        $this->mapTitleGroup($from, $to);
        $this->mapQrCode($from, $to);

        if ($this->mapper) {
            $children = [];
            foreach ($from->getChildren() as $child) {
                $children[] = $this->mapper->map($child);
            }
            $to->setChildren($children);
        }

        return $to;
    }
}
