<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\BaseBookToCodeBook;

use App\Entity\BaseBook;
use App\Entity\CodeBook;
use App\Exception\ApiException;

use function assert;

class BaseBookToCodeBookFrontMatterMapper extends AbstractBaseBookToCodeBookMapper
{
    /** @throws ApiException */
    public function map(BaseBook\FrontMatter $from): CodeBook\FrontMatter
    {
        $to = new CodeBook\FrontMatter();
        $this->mapCommonAttributes($from, $to);
        $this->mapQrCode($from, $to);

        if ($this->mapper) {
            if ($from->getTitlePage()) {
                $titlePage = $this->mapper->map($from->getTitlePage());
                assert($titlePage instanceof CodeBook\TitlePage);
                $to->addChild($titlePage);
            }
            if ($from->getCopyrightPage()) {
                $copyrightPage = $this->mapper->map($from->getCopyrightPage());
                assert($copyrightPage instanceof CodeBook\CopyrightPage);
                $to->addChild($copyrightPage);
            }
            if ($from->getPublisherNote()) {
                $publisherNote = $this->mapper->map($from->getPublisherNote());
                assert($publisherNote instanceof CodeBook\PublisherNote);
                $to->addChild($publisherNote);
            }
            if ($from->getPreface()) {
                $preface = $this->mapper->map($from->getPreface());
                assert($preface instanceof CodeBook\Preface);
                $to->addChild($preface);
            }
            if ($from->getForeword()) {
                $foreword = $this->mapper->map($from->getForeword());
                assert($foreword instanceof CodeBook\Foreword);
                $to->addChild($foreword);
            }

            foreach ($from->getChildren() as $child) {
                $newChild = $this->mapper->map($child);
                $to->addChild($newChild);
            }
        }

        return $to;
    }
}
