{"name": "internationalcodecouncil/correlation-tool", "license": "proprietary", "type": "project", "require": {"php": ">=8.1", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-gmp": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-simplexml": "*", "ext-sqlite3": "*", "ext-tidy": "*", "ext-xml": "*", "ext-xsl": "*", "ext-zip": "*", "doctrine/doctrine-bundle": "^2.15", "doctrine/doctrine-fixtures-bundle": "^3.6", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^2.5", "fileeye/mimemap": "^2.1", "gesdinet/jwt-refresh-token-bundle": "^1.3", "guzzlehttp/guzzle": "^7.3", "internationalcodecouncil/cognito-component": "^1.2", "internationalcodecouncil/content-hub-php-component": "*", "league/flysystem": "^1.1", "league/flysystem-memory": "*", "league/flysystem-ziparchive": "*", "league/uri": "^6.7", "lexik/jwt-authentication-bundle": "^2.21", "nelmio/api-doc-bundle": "^4.26", "nelmio/cors-bundle": "^2.3", "prugala/symfony-request-dto": "^1.5", "psr/simple-cache": "^2.0", "querypath/querypath": "^3.0", "robinvdvleuten/ulid": "^5.0", "sabre/xml": "^4.0", "sensio/framework-extra-bundle": "^6.2", "stof/doctrine-extensions-bundle": "^1.7", "symfony/asset": "*", "symfony/console": "*", "symfony/doctrine-messenger": "6.0.*", "symfony/dotenv": "*", "symfony/expression-language": "*", "symfony/finder": "*", "symfony/flex": "^1.21", "symfony/form": "*", "symfony/framework-bundle": "*", "symfony/http-client": "*", "symfony/intl": "*", "symfony/mailer": "*", "symfony/messenger": "*", "symfony/monolog-bundle": "^3.8", "symfony/process": "*", "symfony/property-access": "*", "symfony/property-info": "*", "symfony/proxy-manager-bridge": "*", "symfony/runtime": "*", "symfony/security-bundle": "*", "symfony/serializer": "*", "symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/uid": "*", "symfony/validator": "*", "symfony/web-link": "*", "symfony/yaml": "*", "twig/extra-bundle": "^3.8", "twig/twig": "^3.8"}, "require-dev": {"dama/doctrine-test-bundle": "^6.0", "fakerphp/faker": "^1.24", "phpunit/phpunit": "^9.5", "rector/rector": "^2.1", "symfony/browser-kit": "*", "symfony/css-selector": "*", "symfony/debug-bundle": "*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "*", "symfony/web-profiler-bundle": "*"}, "repositories": [{"type": "vcs", "no-api": true, "url": "https://github.com/InternationalCodeCouncil/cognito-component.git"}, {"type": "vcs", "url": "https://github.com/InternationalCodeCouncil/aws-cognito"}, {"type": "vcs", "url": "https://github.com/InternationalCodeCouncil/content-hub-php-component.git"}], "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "php-http/discovery": false, "symfony/runtime": true}, "platform": {"php": "8.2"}, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"id": "", "docker": false, "allow-contrib": true, "require": "6.4.*"}}}