<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublisherNoteMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlPublisherNoteMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlPublisherNoteMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlPublisherNoteMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\PublisherNote $from, Xml2\PublisherNote $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    protected function mapCases(): iterable
    {
        $from = new CodeBook\PublisherNote();
        $from->setNodeId('');
        $expected = new Xml2\PublisherNote();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $childSection = new CodeBook\Section();
        $childSection->setNodeId('childNode');
        $from->addChild($childSection);

        $expectedChild = new Xml2\Section();
        $expectedChild->setId('childNode');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
