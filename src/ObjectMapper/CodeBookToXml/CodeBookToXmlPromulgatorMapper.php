<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Serializer\Encoder\Xml2\Element as Xml2;

class CodeBookToXmlPromulgatorMapper extends AbstractCodeBookToXmlMapper
{
    public function map(CodeBook\Promulgator $from): Xml2\Promulgator
    {
        $to = new Xml2\Promulgator();
        $this->mapCommonAttributes($from, $to);
        $this->mapRevisionAttributes($from, $to);

        $address = new Xml2\Address();
        $to->setAddress($address);

        if (!empty($from->getAcronym())) {
            $acronym = new Xml2\Acronym();
            $acronym->setBody($from->getAcronym());
            $to->setAcronym($acronym);
        }

        $address->setAddressLine($from->getAddressLine());
        $address->setOrganizationName($from->getOrganizationName());
        $address->setStreet($from->getStreet());
        $address->setCity($from->getCity());
        $address->setState($from->getState());
        $address->setPostalCode($from->getPostalCode());
        $address->setCountry($from->getCountry());
        $address->setPhone($from->getPhone());
        $address->setFax($from->getFax());

        if (!empty($from->getUrl())) {
            $url = new Xml2\Url();
            $url->setBody($from->getUrl());
            $address->setUrl($url);

            if (!empty($from->getUrlHref())) {
                $url->setHref($from->getUrlHref());
            }

            if (!empty($from->getUrlAlt())) {
                $url->setAlt($from->getUrlAlt());
            }
        }

        if (!empty($from->getEmail())) {
            $email = new Xml2\Email();
            $email->setBody($from->getEmail());
            $address->setEmail($email);
        }

        return $to;
    }
}
