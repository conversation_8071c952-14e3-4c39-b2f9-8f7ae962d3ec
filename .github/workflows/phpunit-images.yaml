name: phpunit - image builder

on:
  workflow_dispatch:
  schedule:
    # weekly on Sunday 10:00 (UTC)
    - cron: '0 10 * * SUN'

jobs:
  build:
    name: Build phpunit-${{ matrix.php-versions }} image

    strategy:
      max-parallel: 2
      matrix:
        php-versions: [ '8.2', '8.3', '8.4' ]

    uses: ./.github/workflows/shared-build.yaml
    with:
      CR_TAG: phpunit-${{ matrix.php-versions }}
      APP_RUNTIME_ENV: dev-server
      PHP_VERSION: ${{ matrix.php-versions }}
    secrets:
      CCT_COMPOSER_AUTH_JSON: ${{ secrets.CCT_COMPOSER_AUTH_JSON }}
      SYMFONY_DECRYPTION_SECRET: ${{ secrets.SYMFONY_DECRYPTION_SECRET_DEV }}
